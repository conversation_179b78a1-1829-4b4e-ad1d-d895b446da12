using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using TacticalCombatSystem.Core;
using TacticalCombatSystem.Interfaces;
using TacticalCombatSystem.Characters;

namespace TacticalCombatSystem.Actions
{
    /// <summary>
    /// Action for using consumable items in battle
    /// </summary>
    [CreateAssetMenu(menuName = "Tactical Combat/Actions/Item Action")]
    public class ItemAction : CombatAction
    {
        [Header("Item Settings")]
        public ConsumableItem itemData;
        public bool consumeOnUse = true;
        public int quantity = 1;
        
        [Header("Animation")]
        [SerializeField] private string animationTrigger = "UseItem";
        public override string AnimationTriggerName => animationTrigger;
        
        public override TargetType TargetType => itemData?.targetType ?? TargetType.Self;
        
        private void OnEnable()
        {
            if (itemData != null)
            {
                actionName = itemData.itemName;
                description = itemData.description;
                validTargets = itemData.validTargets;
                icon = itemData.icon;
            }
            else
            {
                actionName = "Use Item";
                description = "Use a consumable item.";
                validTargets = TargetTeam.Self;
            }
            
            mpCost = 0; // Items don't cost MP
            hpCost = 0;
        }
        
        public override bool CanBeUsedBy(ICombatParticipant user)
        {
            if (!base.CanBeUsedBy(user)) return false;
            
            // Check if user has the item in inventory
            // This would need to be implemented with an inventory system
            // For now, assume items are always available
            return quantity > 0;
        }
        
        public override void Execute(ICombatParticipant user, ICombatParticipant target)
        {
            if (itemData == null)
            {
                Debug.LogError("No item data assigned to ItemAction!");
                return;
            }
            
            Debug.Log($"{user.ParticipantName} uses {itemData.itemName}!");
            
            // Execute item effects
            ExecuteItemEffects(user, target);
            
            // Consume item if specified
            if (consumeOnUse)
            {
                quantity--;
                Debug.Log($"{itemData.itemName} consumed. Remaining: {quantity}");
            }
            
            // Items don't have cooldowns by default, but could be added
            ApplyCooldown();
        }
        
        private void ExecuteItemEffects(ICombatParticipant user, ICombatParticipant target)
        {
            // Healing effects
            if (itemData.healAmount > 0)
            {
                int healAmount = CalculateHealAmount(user);
                
                if (target is BaseCharacter baseTarget)
                {
                    baseTarget.Heal(healAmount);
                }
                else
                {
                    target.CurrentHP = Mathf.Min(target.MaxHP, target.CurrentHP + healAmount);
                    target.OnHealed(healAmount, user);
                }
                
                Debug.Log($"{itemData.itemName} heals {target.ParticipantName} for {healAmount} HP!");
            }
            
            // Mana restoration
            if (itemData.manaAmount > 0)
            {
                int manaAmount = CalculateManaAmount(user);
                int manaToRestore = Mathf.Min(manaAmount, target.MaxMP - target.CurrentMP);
                
                if (manaToRestore > 0)
                {
                    target.CurrentMP += manaToRestore;
                    Debug.Log($"{itemData.itemName} restores {manaToRestore} MP to {target.ParticipantName}!");
                }
            }
            
            // Status effect removal
            if (itemData.removesStatusEffects && itemData.statusEffectsToRemove.Count > 0)
            {
                foreach (string effectName in itemData.statusEffectsToRemove)
                {
                    if (target.HasStatusEffect(effectName))
                    {
                        // This would need a method to remove specific status effects
                        Debug.Log($"{itemData.itemName} removes {effectName} from {target.ParticipantName}!");
                    }
                }
            }
            
            // Apply new status effects
            foreach (var effect in itemData.statusEffectsToApply)
            {
                if (effect != null)
                {
                    var effectInstance = Instantiate(effect);
                    target.ApplyStatusEffect(effectInstance);
                }
            }
            
            // Damage effects (for offensive items like bombs)
            if (itemData.damageAmount > 0)
            {
                int damage = CalculateDamageAmount(user);
                
                if (target is BaseCharacter baseTarget)
                {
                    baseTarget.TakeDamage(damage, user);
                }
                else
                {
                    target.CurrentHP -= damage;
                    target.OnDamageTaken(damage, user);
                }
                
                Debug.Log($"{itemData.itemName} deals {damage} damage to {target.ParticipantName}!");
            }
        }
        
        private int CalculateHealAmount(ICombatParticipant user)
        {
            float healAmount = itemData.healAmount;
            
            // Some items might scale with user's stats
            if (itemData.scaleWithMagicAttack)
            {
                float magicAttack = 0f;
                if (user is BaseCharacter baseChar && baseChar.CharacterData != null)
                {
                    magicAttack = baseChar.CharacterData.GetModifiedStat(StatType.MagicAttack);
                }
                else
                {
                    magicAttack = user.MagicAttack;
                }
                
                healAmount += magicAttack * itemData.scalingFactor;
            }
            
            return Mathf.RoundToInt(healAmount);
        }
        
        private int CalculateManaAmount(ICombatParticipant user)
        {
            // Items typically have fixed mana restoration
            return itemData.manaAmount;
        }
        
        private int CalculateDamageAmount(ICombatParticipant user)
        {
            float damage = itemData.damageAmount;
            
            // Offensive items might scale with user's attack
            if (itemData.scaleWithAttack)
            {
                float attackStat = 0f;
                if (user is BaseCharacter baseChar && baseChar.CharacterData != null)
                {
                    attackStat = itemData.damageType == DamageType.Physical 
                        ? baseChar.CharacterData.GetModifiedStat(StatType.PhysicalAttack)
                        : baseChar.CharacterData.GetModifiedStat(StatType.MagicAttack);
                }
                else
                {
                    attackStat = itemData.damageType == DamageType.Physical ? user.Attack : user.MagicAttack;
                }
                
                damage += attackStat * itemData.scalingFactor;
            }
            
            return Mathf.RoundToInt(damage);
        }
    }
    
    /// <summary>
    /// Data for consumable items
    /// </summary>
    [CreateAssetMenu(menuName = "Tactical Combat/Items/Consumable Item")]
    public class ConsumableItem : ScriptableObject
    {
        [Header("Basic Info")]
        public string itemName = "New Item";
        [TextArea] public string description = "Item description";
        public Sprite icon;
        public int value = 50; // Gold cost
        public int maxStackSize = 99;
        
        [Header("Targeting")]
        public TargetType targetType = TargetType.Self;
        public TargetTeam validTargets = TargetTeam.Ally;
        
        [Header("Effects")]
        public int healAmount = 0;
        public int manaAmount = 0;
        public int damageAmount = 0;
        public DamageType damageType = DamageType.True;
        
        [Header("Scaling")]
        public bool scaleWithMagicAttack = false;
        public bool scaleWithAttack = false;
        public float scalingFactor = 0.1f;
        
        [Header("Status Effects")]
        public bool removesStatusEffects = false;
        public List<string> statusEffectsToRemove = new List<string>();
        public List<StatusEffect> statusEffectsToApply = new List<StatusEffect>();
        
        [Header("Special Properties")]
        public bool isReviveItem = false;
        public float reviveHealthPercentage = 0.25f; // 25% health on revive
        public bool canUseInBattle = true;
        public bool canUseOutOfBattle = true;
    }
}
