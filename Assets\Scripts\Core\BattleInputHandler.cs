using UnityEngine;
using TacticalCombatSystem.UI;

namespace TacticalCombatSystem.Core
{
    /// <summary>
    /// <PERSON>les raw keyboard input and delegates actions to the BattleUI.
    /// </summary>
    public class BattleInputHandler : MonoBehaviour
    {
        public BattleUI battleUI;

        void Update()
        {
            if (battleUI == null)
            {
                return;
            }

            // TODO: Implement input handling methods in BattleUI or use the targeting system
            // The current BattleUI uses Unity's event system for input handling

            // Target Cycling
            if (Input.GetKeyDown(KeyCode.LeftArrow) || Input.GetKeyDown(KeyCode.A))
            {
                // battleUI.CycleTarget(-1); // Method not implemented
                Debug.Log("Cycle target left");
            }
            else if (Input.GetKeyDown(KeyCode.RightArrow) || Input.GetKeyDown(KeyCode.D))
            {
                // battleUI.CycleTarget(1); // Method not implemented
                Debug.Log("Cycle target right");
            }

            // Confirmation
            if (Input.GetKeyDown(KeyCode.Return) || Input.GetKeyDown(KeyCode.Z))
            {
                // battleUI.ConfirmTarget(); // Method not implemented
                Debug.Log("Confirm target");
            }

            // Cancellation
            if (Input.GetKeyDown(KeyCode.Escape) || Input.GetKeyDown(KeyCode.X))
            {
                // battleUI.HandleCancel(); // Method not implemented
                Debug.Log("Cancel action");
            }
        }
    }
}