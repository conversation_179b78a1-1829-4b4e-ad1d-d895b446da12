using UnityEngine;
using TacticalCombatSystem.UI;

namespace TacticalCombatSystem.Core
{
    /// <summary>
    /// <PERSON>les raw keyboard input and delegates actions to the BattleUI.
    /// </summary>
    public class BattleInputHandler : MonoBehaviour
    {
        public BattleUI battleUI;

        void Update()
        {
            if (battleUI == null || !battleUI.IsPlayerInputActive())
            {
                return;
            }

            // Target Cycling
            if (Input.GetKeyDown(KeyCode.LeftArrow) || Input.GetKeyDown(KeyCode.A))
            {
                battleUI.CycleTarget(-1);
            }
            else if (Input.GetKeyDown(KeyCode.RightArrow) || Input.GetKeyDown(KeyCode.D))
            {
                battleUI.CycleTarget(1);
            }

            // Confirmation
            if (Input.GetKeyDown(KeyCode.Return) || Input.GetKeyDown(KeyCode.Z))
            {
                // The UI's EventSystem handles button presses, but we need this for confirming targets.
                battleUI.ConfirmTarget();
            }

            // Cancellation
            if (Input.GetKeyDown(KeyCode.Escape) || Input.GetKeyDown(KeyCode.X))
            {
                battleUI.HandleCancel();
            }
        }
    }
}