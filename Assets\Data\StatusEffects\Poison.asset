%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 0}
  m_Name: Poison
  statusName: Poison
  description: Takes damage each turn. Stacks up to 3 times.
  statusType: 3
  icon: {fileID: 0}
  statusColor: {r: 0.4, g: 0.8, b: 0.2, a: 1}
  duration: 5
  isPermanent: 0
  removeOnCombatEnd: 1
  visualEffectPrefab: {fileID: 0}
  showFloatingText: 1
  floatingText: "Poisoned!"
  statModifiers: []
  isDamageOverTime: 1
  damagePerTurn: 2
  damageScalingStat: 12
  damageScalingPercentage: 0.03
  canStack: 1
  maxStacks: 3
  refreshOnReapply: 1
