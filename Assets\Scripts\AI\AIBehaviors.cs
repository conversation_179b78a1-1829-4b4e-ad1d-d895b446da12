using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using TacticalCombatSystem.Interfaces;
using TacticalCombatSystem.Interfaces.Core;
using TacticalCombatSystem.Core;

namespace TacticalCombatSystem.AI
{
    /// <summary>
    /// Base class for AI behavior implementations
    /// </summary>
    public abstract class AIBehavior
    {
        protected ICombatParticipant aiCharacter;
        protected IBattleManager battleManager;
        
        public AIBehavior(ICombatParticipant character, IBattleManager manager)
        {
            aiCharacter = character;
            battleManager = manager;
        }
        
        /// <summary>
        /// Evaluate an action-target combination for this behavior
        /// </summary>
        public abstract float EvaluateAction(ICombatAction action, ICombatParticipant target);
        
        /// <summary>
        /// Get behavior-specific action preferences
        /// </summary>
        public abstract Dictionary<string, float> GetActionPreferences();
        
        /// <summary>
        /// Check if this behavior has special conditions that override normal decision making
        /// </summary>
        public virtual bool HasOverrideCondition(out AIDecision overrideDecision)
        {
            overrideDecision = null;
            return false;
        }
    }
    
    /// <summary>
    /// Aggressive AI behavior - focuses on dealing maximum damage
    /// </summary>
    public class AggressiveAI : AIBehavior
    {
        public AggressiveAI(ICombatParticipant character, IBattleManager manager) : base(character, manager) { }
        
        public override float EvaluateAction(ICombatAction action, ICombatParticipant target)
        {
            float score = 0f;
            
            if (action.CanTargetEnemies && target.IsPlayerControlled != aiCharacter.IsPlayerControlled)
            {
                // Prioritize high damage actions
                score += 20f;
                
                // Bonus for low-health enemies (finish them off)
                float targetHealthRatio = (float)target.CurrentHP / target.MaxHP;
                score += (1f - targetHealthRatio) * 25f;
                
                // Bonus for high-value targets
                score += target.Attack * 0.2f;
                score += target.MagicAttack * 0.2f;
                
                // Penalty for defensive actions
                if (action.TargetType == TargetType.Self)
                {
                    score -= 10f;
                }
            }
            else
            {
                // Heavy penalty for non-offensive actions
                score -= 15f;
            }
            
            return score;
        }
        
        public override Dictionary<string, float> GetActionPreferences()
        {
            return new Dictionary<string, float>
            {
                { "Attack", 1.0f },
                { "Skill", 0.9f },
                { "Defend", 0.1f },
                { "Item", 0.2f }
            };
        }
        
        public override bool HasOverrideCondition(out AIDecision overrideDecision)
        {
            overrideDecision = null;
            
            // If health is critically low, consider a desperate attack
            float healthRatio = (float)aiCharacter.CurrentHP / aiCharacter.MaxHP;
            if (healthRatio < 0.15f)
            {
                var enemies = battleManager.GetAllParticipants()
                    .Where(p => p.IsPlayerControlled != aiCharacter.IsPlayerControlled && p.IsAlive)
                    .ToList();
                    
                if (enemies.Count > 0)
                {
                    var weakestEnemy = enemies.OrderBy(e => e.CurrentHP).First();
                    var actions = aiCharacter.GetAvailableActions();
                    var strongestAttack = actions.Where(a => a.CanTargetEnemies)
                                                .OrderByDescending(a => a.ManaCost)
                                                .FirstOrDefault();
                    
                    if (strongestAttack != null)
                    {
                        overrideDecision = new AIDecision
                        {
                            action = strongestAttack,
                            target = weakestEnemy,
                            confidence = 0.9f,
                            reasoning = "Desperate attack - low health"
                        };
                        return true;
                    }
                }
            }
            
            return false;
        }
    }
    
    /// <summary>
    /// Defensive AI behavior - focuses on survival and protection
    /// </summary>
    public class DefensiveAI : AIBehavior
    {
        public DefensiveAI(ICombatParticipant character, IBattleManager manager) : base(character, manager) { }
        
        public override float EvaluateAction(ICombatAction action, ICombatParticipant target)
        {
            float score = 0f;
            
            // Prioritize self-preservation
            if (action.TargetType == TargetType.Self)
            {
                score += 15f;
                
                float healthRatio = (float)aiCharacter.CurrentHP / aiCharacter.MaxHP;
                if (healthRatio < 0.5f)
                {
                    score += 20f; // High priority when low health
                }
            }
            
            // Healing allies
            if (action.CanTargetAllies && target != aiCharacter)
            {
                float targetHealthRatio = (float)target.CurrentHP / target.MaxHP;
                score += (1f - targetHealthRatio) * 18f;
            }
            
            // Offensive actions get lower priority
            if (action.CanTargetEnemies)
            {
                score += 5f; // Still some value, but low
                
                // Prefer safe attacks (long range, low risk)
                if (action.range > 1)
                {
                    score += 3f;
                }
            }
            
            return score;
        }
        
        public override Dictionary<string, float> GetActionPreferences()
        {
            return new Dictionary<string, float>
            {
                { "Defend", 1.0f },
                { "Heal", 0.9f },
                { "Buff", 0.8f },
                { "Attack", 0.3f },
                { "Skill", 0.4f }
            };
        }
        
        public override bool HasOverrideCondition(out AIDecision overrideDecision)
        {
            overrideDecision = null;
            
            // Emergency healing
            float healthRatio = (float)aiCharacter.CurrentHP / aiCharacter.MaxHP;
            if (healthRatio < 0.25f)
            {
                var actions = aiCharacter.GetAvailableActions();
                var healingAction = actions.FirstOrDefault(a => a.CanTargetAllies && a.TargetType == TargetType.Self);
                
                if (healingAction != null)
                {
                    overrideDecision = new AIDecision
                    {
                        action = healingAction,
                        target = aiCharacter,
                        confidence = 1.0f,
                        reasoning = "Emergency self-healing"
                    };
                    return true;
                }
            }
            
            return false;
        }
    }
    
    /// <summary>
    /// Support AI behavior - focuses on helping allies
    /// </summary>
    public class SupportAI : AIBehavior
    {
        public SupportAI(ICombatParticipant character, IBattleManager manager) : base(character, manager) { }
        
        public override float EvaluateAction(ICombatAction action, ICombatParticipant target)
        {
            float score = 0f;
            
            // Prioritize ally support
            if (action.CanTargetAllies)
            {
                score += 20f;
                
                if (target != aiCharacter)
                {
                    // Healing wounded allies
                    float targetHealthRatio = (float)target.CurrentHP / target.MaxHP;
                    score += (1f - targetHealthRatio) * 30f;
                    
                    // Prioritize important allies (high damage dealers)
                    score += (target.Attack + target.MagicAttack) * 0.1f;
                }
            }
            
            // Offensive actions only if no allies need help
            if (action.CanTargetEnemies)
            {
                var allies = battleManager.GetAllParticipants()
                    .Where(p => p.IsPlayerControlled == aiCharacter.IsPlayerControlled && p.IsAlive)
                    .ToList();
                    
                float averageAllyHealth = allies.Average(a => (float)a.CurrentHP / a.MaxHP);
                
                if (averageAllyHealth > 0.7f)
                {
                    score += 8f; // Allies are healthy, can attack
                }
                else
                {
                    score += 2f; // Allies need help, low attack priority
                }
            }
            
            return score;
        }
        
        public override Dictionary<string, float> GetActionPreferences()
        {
            return new Dictionary<string, float>
            {
                { "Heal", 1.0f },
                { "Buff", 0.9f },
                { "Support", 0.8f },
                { "Defend", 0.6f },
                { "Attack", 0.2f }
            };
        }
        
        public override bool HasOverrideCondition(out AIDecision overrideDecision)
        {
            overrideDecision = null;
            
            // Find critically wounded ally
            var allies = battleManager.GetAllParticipants()
                .Where(p => p.IsPlayerControlled == aiCharacter.IsPlayerControlled && 
                           p.IsAlive && p != aiCharacter)
                .ToList();
                
            var criticalAlly = allies.FirstOrDefault(a => (float)a.CurrentHP / a.MaxHP < 0.2f);
            
            if (criticalAlly != null)
            {
                var actions = aiCharacter.GetAvailableActions();
                var healingAction = actions.FirstOrDefault(a => a.CanTargetAllies);
                
                if (healingAction != null)
                {
                    overrideDecision = new AIDecision
                    {
                        action = healingAction,
                        target = criticalAlly,
                        confidence = 1.0f,
                        reasoning = "Emergency ally healing"
                    };
                    return true;
                }
            }
            
            return false;
        }
    }
    
    /// <summary>
    /// Tactical AI behavior - balanced approach with strategic thinking
    /// </summary>
    public class TacticalAI : AIBehavior
    {
        public TacticalAI(ICombatParticipant character, IBattleManager manager) : base(character, manager) { }
        
        public override float EvaluateAction(ICombatAction action, ICombatParticipant target)
        {
            float score = 0f;
            
            // Analyze battlefield situation
            var allies = battleManager.GetAllParticipants()
                .Where(p => p.IsPlayerControlled == aiCharacter.IsPlayerControlled && p.IsAlive)
                .ToList();
            var enemies = battleManager.GetAllParticipants()
                .Where(p => p.IsPlayerControlled != aiCharacter.IsPlayerControlled && p.IsAlive)
                .ToList();
                
            float allyHealthRatio = allies.Average(a => (float)a.CurrentHP / a.MaxHP);
            float enemyHealthRatio = enemies.Average(e => (float)e.CurrentHP / e.MaxHP);
            
            // Adaptive strategy based on situation
            if (allyHealthRatio < 0.4f)
            {
                // Team is in trouble - prioritize support
                if (action.CanTargetAllies)
                {
                    score += 25f;
                }
            }
            else if (enemyHealthRatio < 0.3f)
            {
                // Enemies are weak - go for the kill
                if (action.CanTargetEnemies)
                {
                    score += 25f;
                    
                    float targetHealthRatio = (float)target.CurrentHP / target.MaxHP;
                    score += (1f - targetHealthRatio) * 20f;
                }
            }
            else
            {
                // Balanced approach
                if (action.CanTargetEnemies)
                {
                    score += 15f;
                }
                if (action.CanTargetAllies)
                {
                    score += 12f;
                }
                if (action.TargetType == TargetType.Self)
                {
                    score += 10f;
                }
            }
            
            // Consider action efficiency (damage per mana cost)
            if (action.ManaCost > 0)
            {
                float efficiency = 10f / action.ManaCost; // Simple efficiency calculation
                score += efficiency;
            }
            
            return score;
        }
        
        public override Dictionary<string, float> GetActionPreferences()
        {
            return new Dictionary<string, float>
            {
                { "Attack", 0.7f },
                { "Skill", 0.8f },
                { "Heal", 0.6f },
                { "Defend", 0.5f },
                { "Buff", 0.6f }
            };
        }
    }
}
