using System;
using System.Collections.Generic;
using UnityEngine;
using TacticalCombatSystem.Characters;

namespace TacticalCombatSystem.Equipment
{
    [Serializable]
    public enum EquipmentSlot
    {
        Weapon,
        Shield,
        Helmet,
        Armor,
        Boots,
        Gloves,
        Accessory1,
        Accessory2
    }

    [Serializable]
    public enum EquipmentType
    {
        Weapon,
        Armor,
        Accessory
    }

    [Serializable]
    public enum WeaponType
    {
        <PERSON>,
        Axe,
        Spear,
        Bow,
        Staff,
        Dagger,
        <PERSON>,
        Wand
    }

    [Serializable]
    public enum ArmorType
    {
        Light,
        Medium,
        Heavy,
        Cloth,
        Leather,
        Mail,
        Plate
    }

    /// <summary>
    /// Base class for all equipment items
    /// </summary>
    [CreateAssetMenu(fileName = "New Equipment", menuName = "Tactical Combat/Equipment/Equipment Item")]
    public class EquipmentItem : ScriptableObject
    {
        [Header("Basic Info")]
        public string itemName = "New Equipment";
        [TextArea] public string description = "Equipment description";
        public Sprite icon;
        public GameObject visualPrefab; // 3D model for the equipment
        
        [Header("Equipment Properties")]
        public EquipmentSlot slot;
        public EquipmentType equipmentType;
        public int level = 1;
        public int value = 100; // Gold value
        public float weight = 1f;
        
        [Header("Requirements")]
        public int levelRequirement = 1;
        public List<StatRequirement> statRequirements = new List<StatRequirement>();
        
        [Header("Stat Modifiers")]
        public List<StatModifier> statModifiers = new List<StatModifier>();
        
        [Header("Special Effects")]
        public List<string> specialEffects = new List<string>(); // For unique equipment abilities
        
        [Header("Weapon Specific")]
        [SerializeField] private WeaponType weaponType;
        [SerializeField] private int minDamage = 1;
        [SerializeField] private int maxDamage = 5;
        [SerializeField] private float criticalChanceBonus = 0f;
        [SerializeField] private int range = 1;
        
        [Header("Armor Specific")]
        [SerializeField] private ArmorType armorType;
        [SerializeField] private int armorValue = 0;
        [SerializeField] private int magicResistance = 0;
        
        // Properties
        public WeaponType WeaponType => weaponType;
        public int MinDamage => minDamage;
        public int MaxDamage => maxDamage;
        public float CriticalChanceBonus => criticalChanceBonus;
        public int Range => range;
        public ArmorType ArmorType => armorType;
        public int ArmorValue => armorValue;
        public int MagicResistance => magicResistance;

        /// <summary>
        /// Check if a character can equip this item
        /// </summary>
        public bool CanBeEquippedBy(BaseCharacter character)
        {
            if (character == null) return false;
            
            // Check level requirement
            if (character.CharacterData.Level < levelRequirement) return false;
            
            // Check stat requirements
            foreach (var requirement in statRequirements)
            {
                float characterStat = character.CharacterData.GetModifiedStat(requirement.statType);
                if (characterStat < requirement.minimumValue) return false;
            }
            
            return true;
        }

        /// <summary>
        /// Get the total stat bonus this equipment provides
        /// </summary>
        public float GetStatBonus(StatType statType)
        {
            float total = 0f;
            foreach (var modifier in statModifiers)
            {
                if (modifier.statType == statType)
                {
                    total += modifier.value;
                }
            }
            return total;
        }

        /// <summary>
        /// Calculate weapon damage
        /// </summary>
        public int CalculateDamage()
        {
            if (equipmentType != EquipmentType.Weapon) return 0;
            return UnityEngine.Random.Range(minDamage, maxDamage + 1);
        }
    }

    [Serializable]
    public class StatRequirement
    {
        public StatType statType;
        public float minimumValue;
    }

    /// <summary>
    /// Manages a character's equipped items
    /// </summary>
    [Serializable]
    public class EquipmentManager
    {
        [SerializeField] private Dictionary<EquipmentSlot, EquipmentItem> equippedItems = new Dictionary<EquipmentSlot, EquipmentItem>();
        [SerializeField] private BaseCharacter owner;
        
        // Events
        public event Action<EquipmentSlot, EquipmentItem> OnItemEquipped;
        public event Action<EquipmentSlot, EquipmentItem> OnItemUnequipped;

        public EquipmentManager(BaseCharacter character)
        {
            owner = character;
            InitializeSlots();
        }

        private void InitializeSlots()
        {
            foreach (EquipmentSlot slot in System.Enum.GetValues(typeof(EquipmentSlot)))
            {
                equippedItems[slot] = null;
            }
        }

        /// <summary>
        /// Equip an item to the specified slot
        /// </summary>
        public bool EquipItem(EquipmentItem item, EquipmentSlot? forceSlot = null)
        {
            if (item == null) return false;
            
            // Check if character can equip this item
            if (!item.CanBeEquippedBy(owner))
            {
                Debug.LogWarning($"{owner.Name} cannot equip {item.itemName} - requirements not met");
                return false;
            }
            
            EquipmentSlot targetSlot = forceSlot ?? item.slot;
            
            // Unequip current item in slot if any
            if (equippedItems[targetSlot] != null)
            {
                UnequipItem(targetSlot);
            }
            
            // Equip new item
            equippedItems[targetSlot] = item;
            ApplyItemModifiers(item);
            
            OnItemEquipped?.Invoke(targetSlot, item);
            Debug.Log($"{owner.Name} equipped {item.itemName}");
            
            return true;
        }

        /// <summary>
        /// Unequip item from the specified slot
        /// </summary>
        public EquipmentItem UnequipItem(EquipmentSlot slot)
        {
            EquipmentItem item = equippedItems[slot];
            if (item == null) return null;
            
            equippedItems[slot] = null;
            RemoveItemModifiers(item);
            
            OnItemUnequipped?.Invoke(slot, item);
            Debug.Log($"{owner.Name} unequipped {item.itemName}");
            
            return item;
        }

        /// <summary>
        /// Get equipped item in slot
        /// </summary>
        public EquipmentItem GetEquippedItem(EquipmentSlot slot)
        {
            return equippedItems.TryGetValue(slot, out EquipmentItem item) ? item : null;
        }

        /// <summary>
        /// Get all equipped items
        /// </summary>
        public Dictionary<EquipmentSlot, EquipmentItem> GetAllEquippedItems()
        {
            return new Dictionary<EquipmentSlot, EquipmentItem>(equippedItems);
        }

        /// <summary>
        /// Check if a slot is empty
        /// </summary>
        public bool IsSlotEmpty(EquipmentSlot slot)
        {
            return equippedItems[slot] == null;
        }

        /// <summary>
        /// Apply stat modifiers from equipped item
        /// </summary>
        private void ApplyItemModifiers(EquipmentItem item)
        {
            if (owner?.CharacterData?.Stats == null) return;
            
            foreach (var modifier in item.statModifiers)
            {
                var equipmentModifier = new StatModifier(
                    modifier.statType,
                    modifier.modifierType,
                    modifier.value,
                    $"Equipment:{item.itemName}",
                    1 // Equipment has priority 1
                );
                
                owner.CharacterData.Stats.AddModifier(equipmentModifier);
            }
        }

        /// <summary>
        /// Remove stat modifiers from unequipped item
        /// </summary>
        private void RemoveItemModifiers(EquipmentItem item)
        {
            if (owner?.CharacterData?.Stats == null) return;
            
            owner.CharacterData.Stats.RemoveModifiersFromSource($"Equipment:{item.itemName}");
        }

        /// <summary>
        /// Calculate total equipment weight
        /// </summary>
        public float GetTotalWeight()
        {
            float totalWeight = 0f;
            foreach (var item in equippedItems.Values)
            {
                if (item != null)
                {
                    totalWeight += item.weight;
                }
            }
            return totalWeight;
        }

        /// <summary>
        /// Calculate total equipment value
        /// </summary>
        public int GetTotalValue()
        {
            int totalValue = 0;
            foreach (var item in equippedItems.Values)
            {
                if (item != null)
                {
                    totalValue += item.value;
                }
            }
            return totalValue;
        }
    }
}
