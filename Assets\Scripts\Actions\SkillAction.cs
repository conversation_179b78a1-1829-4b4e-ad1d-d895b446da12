using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using TacticalCombatSystem.Core;
using TacticalCombatSystem.Interfaces;
using TacticalCombatSystem.Characters;

namespace TacticalCombatSystem.Actions
{
    /// <summary>
    /// Advanced skill action with multiple effects and targeting options
    /// </summary>
    [CreateAssetMenu(menuName = "Tactical Combat/Actions/Skill Action")]
    public class SkillAction : CombatAction
    {
        [Header("Skill Settings")]
        public SkillType skillType = SkillType.Damage;
        public int basePower = 20;
        public DamageType damageType = DamageType.Magical;
        public ElementType elementType = ElementType.None;
        
        [Header("Targeting")]
        public bool isAreaOfEffect = false;
        public int aoeRadius = 1;
        public bool affectsAllies = false;
        public bool affectsSelf = false;
        
        [Header("Healing")]
        public int baseHealAmount = 0;
        public bool scaleWithMagicAttack = true;
        public float healingScaling = 0.8f;
        
        [Header("Status Effects")]
        public float statusEffectChance = 1.0f;
        public bool guaranteedStatusEffect = false;
        
        [Header("Special Effects")]
        public bool pierceDefense = false;
        public bool ignoreEvasion = false;
        public int multiHitCount = 1;
        public float multiHitDamageReduction = 0.8f;
        
        [Header("Animation")]
        [SerializeField] private string animationTrigger = "Skill";
        public override string AnimationTriggerName => animationTrigger;
        
        public override TargetType TargetType => isAreaOfEffect ? TargetType.AllEnemies : TargetType.SingleEnemy;
        
        private void OnEnable()
        {
            // Set up default values based on skill type
            switch (skillType)
            {
                case SkillType.Damage:
                    actionName = "Damage Skill";
                    description = "A skill that deals damage to enemies.";
                    validTargets = TargetTeam.Enemy;
                    break;
                case SkillType.Heal:
                    actionName = "Heal Skill";
                    description = "A skill that heals allies.";
                    validTargets = TargetTeam.Ally;
                    break;
                case SkillType.Buff:
                    actionName = "Buff Skill";
                    description = "A skill that enhances allies.";
                    validTargets = TargetTeam.Ally;
                    break;
                case SkillType.Debuff:
                    actionName = "Debuff Skill";
                    description = "A skill that weakens enemies.";
                    validTargets = TargetTeam.Enemy;
                    break;
                case SkillType.Utility:
                    actionName = "Utility Skill";
                    description = "A skill with special effects.";
                    validTargets = TargetTeam.All;
                    break;
            }
        }
        
        public override void Execute(ICombatParticipant user, ICombatParticipant target)
        {
            Debug.Log($"{user.ParticipantName} uses {actionName}!");

            // Pay costs
            user.CurrentMP -= mpCost;
            user.CurrentHP = Mathf.Max(1, user.CurrentHP - hpCost);
            
            // Get all targets (for AoE skills)
            List<ICombatParticipant> targets = GetTargetsForExecution(user, target);
            
            // Execute skill on all targets
            foreach (var skillTarget in targets)
            {
                ExecuteOnSingleTarget(user, skillTarget);
            }
            
            // Apply cooldown
            ApplyCooldown();
        }
        
        private List<ICombatParticipant> GetTargetsForExecution(ICombatParticipant user, ICombatParticipant primaryTarget)
        {
            var targets = new List<ICombatParticipant>();
            
            if (!isAreaOfEffect)
            {
                targets.Add(primaryTarget);
                return targets;
            }
            
            // For AoE, get all valid targets based on the skill settings
            // This is a simplified implementation - in a real tactical game, 
            // you'd check actual positions and ranges
            
            // Get battle manager to access all participants
            var battleManager = FindFirstObjectByType<TacticalCombatSystem.Battle.BattleManager>();
            if (battleManager == null) 
            {
                targets.Add(primaryTarget);
                return targets;
            }
            
            var allParticipants = battleManager.GetAllParticipants();
            
            foreach (var participant in allParticipants)
            {
                if (!participant.IsAlive) continue;
                
                bool isValidTarget = false;
                
                switch (validTargets)
                {
                    case TargetTeam.Enemy:
                        isValidTarget = participant.IsPlayerControlled != user.IsPlayerControlled;
                        break;
                    case TargetTeam.Ally:
                        isValidTarget = participant.IsPlayerControlled == user.IsPlayerControlled && participant != user;
                        break;
                    case TargetTeam.Allies:
                        isValidTarget = participant.IsPlayerControlled == user.IsPlayerControlled;
                        break;
                    case TargetTeam.All:
                        isValidTarget = true;
                        break;
                    case TargetTeam.Self:
                        isValidTarget = participant == user;
                        break;
                }
                
                if (isValidTarget)
                {
                    targets.Add(participant);
                }
            }
            
            return targets;
        }
        
        private void ExecuteOnSingleTarget(ICombatParticipant user, ICombatParticipant target)
        {
            // Execute multiple hits if specified
            for (int hit = 0; hit < multiHitCount; hit++)
            {
                float hitDamageMultiplier = hit == 0 ? 1f : multiHitDamageReduction;
                
                switch (skillType)
                {
                    case SkillType.Damage:
                        ExecuteDamage(user, target, hitDamageMultiplier);
                        break;
                    case SkillType.Heal:
                        ExecuteHeal(user, target);
                        break;
                    case SkillType.Buff:
                    case SkillType.Debuff:
                    case SkillType.Utility:
                        ExecuteStatusEffects(user, target);
                        break;
                }
                
                // Small delay between hits for multi-hit skills
                if (multiHitCount > 1 && hit < multiHitCount - 1)
                {
                    // In a real implementation, you'd use a coroutine or animation events
                    // For now, just log the hit
                    Debug.Log($"Hit {hit + 1} of {multiHitCount}");
                }
            }
        }
        
        private void ExecuteDamage(ICombatParticipant user, ICombatParticipant target, float damageMultiplier = 1f)
        {
            // Check accuracy (unless skill ignores evasion)
            if (!ignoreEvasion && !CheckAccuracy(user, target))
            {
                Debug.Log($"{actionName} missed {target.ParticipantName}!");
                return;
            }
            
            // Calculate damage
            var damageResult = CalculateSkillDamage(user, target, damageMultiplier);
            
            if (damageResult.damage > 0)
            {
                // Apply damage
                if (target is BaseCharacter baseTarget)
                {
                    baseTarget.TakeDamage(damageResult.damage, user);
                }
                else
                {
                    target.CurrentHP -= damageResult.damage;
                    target.OnDamageTaken(damageResult.damage, user);
                }
                
                // Log results
                string critText = damageResult.isCritical ? " (Critical Hit!)" : "";
                string elementText = elementType != ElementType.None ? $" ({elementType})" : "";
                Debug.Log($"{actionName} deals {damageResult.damage}{elementText} damage to {target.ParticipantName}{critText}");
            }
            
            // Apply status effects
            ExecuteStatusEffects(user, target);
        }
        
        private void ExecuteHeal(ICombatParticipant user, ICombatParticipant target)
        {
            int healAmount = CalculateHealAmount(user);
            
            if (healAmount > 0)
            {
                if (target is BaseCharacter baseTarget)
                {
                    baseTarget.Heal(healAmount);
                }
                else
                {
                    target.CurrentHP = Mathf.Min(target.MaxHP, target.CurrentHP + healAmount);
                    target.OnHealed(healAmount, user);
                }
                
                Debug.Log($"{actionName} heals {target.ParticipantName} for {healAmount} HP!");
            }
            
            // Apply status effects (like regeneration buffs)
            ExecuteStatusEffects(user, target);
        }
        
        private void ExecuteStatusEffects(ICombatParticipant user, ICombatParticipant target)
        {
            foreach (var effect in statusEffects)
            {
                if (effect == null) continue;
                
                // Check if status effect should be applied
                bool shouldApply = guaranteedStatusEffect || Random.value <= statusEffectChance;
                
                if (shouldApply)
                {
                    var effectInstance = Instantiate(effect);
                    target.ApplyStatusEffect(effectInstance);
                }
            }
        }
        
        private bool CheckAccuracy(ICombatParticipant user, ICombatParticipant target)
        {
            // Skills typically have higher base accuracy than basic attacks
            float accuracy = 0.98f; // 98% base accuracy for skills
            float evasion = 0f;
            
            if (user is BaseCharacter userChar && userChar.CharacterData != null)
            {
                accuracy = userChar.CharacterData.GetModifiedStat(StatType.Accuracy);
            }
            
            if (target is BaseCharacter targetChar && targetChar.CharacterData != null)
            {
                evasion = targetChar.CharacterData.GetModifiedStat(StatType.Evasion);
            }
            
            float hitChance = Mathf.Clamp01(accuracy - evasion);
            return Random.value <= hitChance;
        }
        
        private DamageResult CalculateSkillDamage(ICombatParticipant user, ICombatParticipant target, float damageMultiplier)
        {
            var result = new DamageResult();
            result.damageType = damageType;
            result.element = elementType;
            
            // Base power
            float damage = basePower * damageMultiplier;
            
            // Add user's attack stat
            float attackStat = GetAttackStat(user);
            damage += attackStat;
            
            // Apply target's defense (unless piercing)
            if (!pierceDefense)
            {
                float defenseStat = GetDefenseStat(target);
                damage *= 100f / (100f + defenseStat);
            }
            
            // Check for critical hit
            result.isCritical = CheckCriticalHit(user);
            if (result.isCritical)
            {
                damage *= GetCriticalMultiplier(user);
            }
            
            // Apply elemental modifiers
            damage *= GetElementalModifier(target);
            
            // Apply defend bonus
            if (target.IsDefending)
            {
                damage *= 0.7f; // Skills are less affected by defending
            }
            
            result.damage = Mathf.Max(1, Mathf.RoundToInt(damage));
            return result;
        }
        
        private int CalculateHealAmount(ICombatParticipant user)
        {
            float healAmount = baseHealAmount;
            
            if (scaleWithMagicAttack)
            {
                float magicAttack = 0f;
                if (user is BaseCharacter baseChar && baseChar.CharacterData != null)
                {
                    magicAttack = baseChar.CharacterData.GetModifiedStat(StatType.MagicAttack);
                }
                else
                {
                    magicAttack = user.MagicAttack;
                }
                
                healAmount += magicAttack * healingScaling;
            }
            
            return Mathf.RoundToInt(healAmount);
        }
        
        // Helper methods (similar to BasicAttackAction)
        private float GetAttackStat(ICombatParticipant user)
        {
            if (user is BaseCharacter baseChar && baseChar.CharacterData != null)
            {
                return damageType switch
                {
                    DamageType.Physical => baseChar.CharacterData.GetModifiedStat(StatType.PhysicalAttack),
                    DamageType.Magical => baseChar.CharacterData.GetModifiedStat(StatType.MagicAttack),
                    DamageType.True => 0f,
                    _ => baseChar.CharacterData.GetModifiedStat(StatType.MagicAttack)
                };
            }
            
            return damageType switch
            {
                DamageType.Physical => user.Attack,
                DamageType.Magical => user.MagicAttack,
                DamageType.True => 0f,
                _ => user.MagicAttack
            };
        }
        
        private float GetDefenseStat(ICombatParticipant target)
        {
            if (target is BaseCharacter baseChar && baseChar.CharacterData != null)
            {
                return damageType switch
                {
                    DamageType.Physical => baseChar.CharacterData.GetModifiedStat(StatType.PhysicalDefense),
                    DamageType.Magical => baseChar.CharacterData.GetModifiedStat(StatType.MagicDefense),
                    DamageType.True => 0f,
                    _ => baseChar.CharacterData.GetModifiedStat(StatType.MagicDefense)
                };
            }
            
            return damageType switch
            {
                DamageType.Physical => target.Defense,
                DamageType.Magical => target.MagicDefense,
                DamageType.True => 0f,
                _ => target.MagicDefense
            };
        }
        
        private bool CheckCriticalHit(ICombatParticipant user)
        {
            float critChance = 0.05f; // Base 5% crit for skills
            
            if (user is BaseCharacter baseChar && baseChar.CharacterData != null)
            {
                critChance = baseChar.CharacterData.GetModifiedStat(StatType.CriticalChance);
            }
            
            return Random.value <= critChance;
        }
        
        private float GetCriticalMultiplier(ICombatParticipant user)
        {
            if (user is BaseCharacter baseChar && baseChar.CharacterData != null)
            {
                return baseChar.CharacterData.GetModifiedStat(StatType.CriticalDamage);
            }
            
            return 1.5f; // Default crit multiplier
        }
        
        private float GetElementalModifier(ICombatParticipant target)
        {
            if (elementType == ElementType.None) return 1f;
            
            if (target is BaseCharacter baseChar && baseChar.CharacterData != null)
            {
                StatType resistanceStat = elementType switch
                {
                    ElementType.Fire => StatType.FireResistance,
                    ElementType.Ice => StatType.IceResistance,
                    ElementType.Lightning => StatType.LightningResistance,
                    ElementType.Earth => StatType.EarthResistance,
                    ElementType.Wind => StatType.WindResistance,
                    ElementType.Water => StatType.WaterResistance,
                    ElementType.Light => StatType.LightResistance,
                    ElementType.Dark => StatType.DarkResistance,
                    _ => StatType.MagicDefense
                };
                
                float resistance = baseChar.CharacterData.GetModifiedStat(resistanceStat);
                return Mathf.Clamp(1f - resistance, 0.1f, 2f);
            }
            
            return 1f;
        }
    }
    
    public enum SkillType
    {
        Damage,
        Heal,
        Buff,
        Debuff,
        Utility
    }
}
