using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using TacticalCombatSystem.Interfaces;
using TacticalCombatSystem.Abilities;

namespace TacticalCombatSystem.Core
{
    /// <summary>
    /// Base class for all combat actions (attacks, skills, items, etc.)
    /// </summary>
    public abstract class CombatAction : ScriptableObject, ICombatAction
    {
        [Header("Basic Info")]
        public string actionName = "New Action";
        [TextArea] public string description = "Action description";
        public Sprite icon;
        
        [Header("Targeting")]

        public TacticalCombatSystem.Interfaces.TargetTeam validTargets;
        public int range = 1;
        public bool requiresLineOfSight = true;
        
        [Header("Costs")]
        public int mpCost = 0;
        public int hpCost = 0;
        public int cooldown = 0;
        
        [Header("Effects")]
        public List<StatusEffect> statusEffects = new List<StatusEffect>();

        [Header("Menu Settings")]
        [Tooltip("The category this action belongs to in the battle menu")]
        [SerializeField] private ActionCategory _category = ActionCategory.Basic;
        [Tooltip("The type of resource this action consumes")]
        [SerializeField] private ResourceType _resourceType = ResourceType.None;
        [Tooltip("The amount of resources this action consumes")]
        [SerializeField] private int _resourceCost = 0;

        // Cooldown tracking (not serialized)
        [NonSerialized] private int currentCooldown = 0;

        // ICombatAction interface properties
        public string ActionName => actionName;
        public string Description => description;
        public Sprite Icon => icon;
        public bool CanTargetSelf => validTargets == (TargetTeam)TacticalCombatSystem.Interfaces.TargetTeam.Self || 
                                   validTargets == (TargetTeam)TacticalCombatSystem.Interfaces.TargetTeam.Allies || 
                                   validTargets == (TargetTeam)TacticalCombatSystem.Interfaces.TargetTeam.All;
        public bool CanTargetAllies => validTargets == (TargetTeam)TacticalCombatSystem.Interfaces.TargetTeam.Ally || 
                                     validTargets == (TargetTeam)TacticalCombatSystem.Interfaces.TargetTeam.Allies || 
                                     validTargets == (TargetTeam)TacticalCombatSystem.Interfaces.TargetTeam.All;
        public bool CanTargetEnemies => validTargets == (TargetTeam)TacticalCombatSystem.Interfaces.TargetTeam.Enemy || 
                                      validTargets == (TargetTeam)TacticalCombatSystem.Interfaces.TargetTeam.All;
        public TacticalCombatSystem.Interfaces.TargetTeam ValidTargets => (TacticalCombatSystem.Interfaces.TargetTeam)validTargets;
        public int ManaCost => mpCost;
        public int Cooldown => cooldown;
        public int CurrentCooldown
        {
            get => currentCooldown;
            set => currentCooldown = value;
        }

        // Menu System implementation
        public ActionCategory Category => _category;
        public ResourceType ResourceType => _resourceType;
        public int ResourceCost => _resourceCost;

        public abstract string AnimationTriggerName { get; }

        public abstract TargetType TargetType { get; }
        
        /// <summary>
        /// Check if the action can be performed by the user
        /// </summary>
        public virtual bool CanBeUsedBy(ICombatParticipant user)
        {
            if (user == null || !user.IsAlive) return false;
            if (user.CurrentMP < mpCost) return false;
            if (user.CurrentHP <= hpCost) return false;
            if (currentCooldown > 0) return false;
            
            return true;
        }
        
        /// <summary>
        /// Check if the target is valid for this action
        /// </summary>
        public virtual bool IsValidTarget(ICombatParticipant user, ICombatParticipant target)
        {
            if (target == null) return false;
            
            // Check if target is on the right team
            switch (validTargets)
            {
                case TargetTeam.Self:
                    return target == user;
                case TargetTeam.Ally:
                    return target.IsPlayerControlled == user.IsPlayerControlled && target != user;
                case TargetTeam.Allies:
                    return target.IsPlayerControlled == user.IsPlayerControlled;
                case TargetTeam.Enemy:
                    return target.IsPlayerControlled != user.IsPlayerControlled;
                case TargetTeam.All:
                    return true;
                default:
                    return false;
            }
        }
        
        /// <summary>
        /// Get all valid targets for this action
        /// </summary>
        public virtual List<ICombatParticipant> GetValidTargets(ICombatParticipant user, List<ICombatParticipant> allParticipants)
        {
            List<ICombatParticipant> validTargets = new List<ICombatParticipant>();
            
            foreach (var participant in allParticipants)
            {
                if (IsValidTarget(user, participant))
                {
                    validTargets.Add(participant);
                }
            }
            
            return validTargets;
        }
        
        /// <summary>
        /// Execute the action (ICombatAction interface implementation)
        /// </summary>
        public abstract void Execute(ICombatParticipant user, ICombatParticipant target);

        /// <summary>
        /// Execute the action with coroutine support, calling the abstract Execute method.
        /// </summary>
        public virtual IEnumerator ExecuteCoroutine(ICombatParticipant user, ICombatParticipant target, System.Action onComplete)
        {
            Execute(user, target);
            yield return null; // Wait a frame to ensure it's treated as a coroutine.
            onComplete?.Invoke();
        }
        
        /// <summary>
        /// Called when the action is selected in the UI
        /// </summary>
        public virtual void OnSelected(ICombatParticipant user) { }
        
        /// <summary>
        /// Called when the action is deselected in the UI
        /// </summary>
        public virtual void OnDeselected(ICombatParticipant user) { }
        
        /// <summary>
        /// Apply cooldown to this action
        /// </summary>
        public void ApplyCooldown()
        {
            if (cooldown > 0)
            {
                currentCooldown = cooldown;
            }
        }
        
        /// <summary>
        /// Reduce cooldown by the specified amount (usually 1 per turn)
        /// </summary>
        public void ReduceCooldown(int amount = 1)
        {
            currentCooldown = Mathf.Max(0, currentCooldown - amount);
        }
        
        /// <summary>
        /// Get the current cooldown
        /// </summary>
        public int GetCurrentCooldown() => currentCooldown;
        
        /// <summary>
        /// Reset cooldown to 0
        /// </summary>
        public void ResetCooldown()
        {
            currentCooldown = 0;
        }

        /// <summary>
        /// Play visual effects for the action (ICombatAction interface implementation)
        /// </summary>
        public virtual void PlayVFX(Vector3 position)
        {
            // Default implementation - derived classes can override
            // Could instantiate particle effects or other visual feedback
        }
    }
    

    // TargetTeam enum moved to TacticalCombatSystem.Interfaces.TargetTeam
    // Using the interface's enum for consistency

    // ResourceType moved to avoid duplicate definition
}
