using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using TacticalCombatSystem.Core;
using TacticalCombatSystem.Interfaces;
using TacticalCombatSystem.Actions;
using TacticalCombatSystem.Characters;
using UnityEngine;
using TacticalCombatSystem.Abilities;
using TacticalCombatSystem.Interfaces.Core;
using TacticalCombatSystem.Camera;

namespace TacticalCombatSystem.Battle
{
    public class BattleManager : MonoBehaviour, IBattleManager
    {
        public bool IsPlayerTurn { get; private set; }

        // Explicit interface implementation
        IBattleManager IBattleManager.Instance => this;

        // Events
        public event Action OnBattleStarted;
        public event Action<ICombatParticipant> OnTurnStarted;
        public event Action<ICombatParticipant, int> OnCharacterDamaged;
        public event Action<ICombatParticipant, int> OnCharacterHealed;
        public event Action<ICombatParticipant, object> OnStatusEffectApplied;
        public event Action<bool> OnBattleEnded;
        public event Action<ICombatParticipant> OnTurnEnded;
        public event Action<ICombatParticipant, ICombatParticipant> OnActionExecuting;

        public event System.Action<ICombatParticipant> CharacterSelected;
        public event System.Action<ICombatParticipant> CharacterDeselected;

        // Explicit interface implementations
        event System.Action<ICombatParticipant> IBattleManager.CharacterSelected { add => CharacterSelected += value; remove => CharacterSelected -= value; }
        event System.Action<ICombatParticipant> IBattleManager.CharacterDeselected { add => CharacterDeselected += value; remove => CharacterDeselected -= value; }

        // Current battle state
        private ICombatParticipant currentTurnCharacter;
        private List<ICombatParticipant> turnOrder = new List<ICombatParticipant>();
        private int currentTurnIndex = 0;
        private TurnOrderSystem turnOrderSystem;

        [Header("Battle State")]
        [SerializeField] private List<ICombatParticipant> playerTeam = new List<ICombatParticipant>();
        [SerializeField] private List<ICombatParticipant> enemyTeam = new List<ICombatParticipant>();

        // Singleton pattern
        public static IBattleManager Instance { get; private set; }
        private BattleCameraController _cameraController;

        private bool _awaitingPlayerInput = false;
        private bool battleActive = true;

        public List<BaseCharacter> Participants { get; private set; }

        // Public properties to expose the private fields
        public bool IsAwaitingPlayerInput => _awaitingPlayerInput;
        public bool IsBattleActive => battleActive;

        private void Awake()
        {
            if (Instance != null && (UnityEngine.Object)Instance != this)
            {
                Debug.LogWarning($"Duplicate BattleManager on object '{gameObject.name}' is destroying itself.", gameObject);
                Destroy(gameObject);
                return;
            }

            Instance = this;
            DontDestroyOnLoad(transform.root.gameObject);

            // Initialize enhanced turn order system
            turnOrderSystem = new TurnOrderSystem();
        }

        private void Start()
        {
            Participants = new List<BaseCharacter>();
            InitializeBattle();
            OnBattleStarted?.Invoke();

            // Start the first turn directly (no coroutine)
            if (turnOrder.Count > 0)
            {
                StartTurn(turnOrder[0]);
            }

            // Camera setup
            _cameraController = BattleCameraController.Instance;
            if (_cameraController != null)
            {
                OnTurnStarted += HandleTurnStartedCamera;
                OnActionExecuting += HandleActionExecutingCamera;
                OnBattleEnded += HandleBattleEndedCamera;
            }
            else
            {
                Debug.LogWarning("BattleCameraController not found. Cinematic cameras disabled.");
            }
        }

        public void StartBattle()
        {
            InitializeBattle();
        }

        public void EndBattle(bool playerWon)
        {
            StopAllCoroutines();
            battleActive = false;
            Debug.Log(playerWon ? "Player won!" : "Player lost!");
            IsPlayerTurn = false;

            if (_cameraController != null)
            {
                OnTurnStarted -= HandleTurnStartedCamera;
                OnActionExecuting -= HandleActionExecutingCamera;
                OnBattleEnded -= HandleBattleEndedCamera;
            }

            OnBattleEnded?.Invoke(playerWon);
        }

        public void StartTurn(ICombatParticipant character)
        {
            StartTurnInternal(character);
        }

        private void StartTurnInternal(ICombatParticipant character)
        {
            if (character == null) return;

            currentTurnCharacter = character;
            IsPlayerTurn = playerTeam.Contains(character);
            character.OnTurnStart();
            OnTurnStarted?.Invoke(character);

            if (!IsPlayerTurn && character is MonoBehaviour)
            {
                // Start AI turn as coroutine
                StartCoroutine(AITurn(character));
            }
            else if (IsPlayerTurn)
            {
                // Set flag to wait for player input
                _awaitingPlayerInput = true;
            }
        }

        public void EndTurn()
        {
            if (currentTurnCharacter == null) return;

            OnTurnEnded?.Invoke(currentTurnCharacter);
            currentTurnCharacter.OnTurnEnd();

            if (CheckBattleEnd()) return;

            NextTurn();
        }

        private bool CheckBattleEnd()
        {
            bool playerTeamWiped = playerTeam.All(p => !p.IsAlive);
            bool enemyTeamWiped = enemyTeam.All(p => !p.IsAlive);

            if (playerTeamWiped)
            {
                EndBattle(false);
                return true;
            }
            if (enemyTeamWiped)
            {
                EndBattle(true);
                return true;
            }
            return false;
        }

        private void InitializeBattle()
        {
            var allParticipants = GetAllParticipants();
            foreach (var participant in allParticipants)
            {
                participant.InitializeForBattle(this);
            }
            UpdateTurnOrder();

            // Initialize enhanced turn order system
            if (turnOrderSystem != null)
            {
                turnOrderSystem.InitializeTurnOrder(allParticipants);
            }
        }

        private void UpdateTurnOrder()
        {
            var allParticipants = GetAllParticipants();
            turnOrder = allParticipants.Where(p => p.IsAlive).OrderByDescending(p => p.Speed).ToList();
        }

        private void NextTurn()
        {
            ICombatParticipant nextCharacter = null;

            // Use enhanced turn order system if available
            if (turnOrderSystem != null)
            {
                nextCharacter = turnOrderSystem.GetNextParticipant();
            }
            else
            {
                // Fallback to legacy system
                if (turnOrder.Count == 0) return;

                int attempts = 0;
                do
                {
                    currentTurnIndex = (currentTurnIndex + 1) % turnOrder.Count;
                    attempts++;
                    // Prevent an infinite loop if all characters are dead (CheckBattleEnd should catch this, but as a safeguard)
                    if (attempts > turnOrder.Count * 2)
                    {
                        EndTurn();
                        Debug.LogError("No living character found to start a turn. Ending battle to prevent infinite loop.");
                        EndTurn();
                        EndBattle(false);
                        return;
                    }
                } while (!turnOrder[currentTurnIndex].IsAlive);

                nextCharacter = turnOrder[currentTurnIndex];
            }

            if (nextCharacter != null)
            {
                StartTurn(nextCharacter);
            }
            else
            {
                Debug.LogError("No valid character found for next turn. Ending battle.");
                EndBattle(false);
            }
        }

        public void AddToPlayerTeam(ICombatParticipant character)
        {
            if (!playerTeam.Contains(character)) playerTeam.Add(character);
        }

        public void AddToEnemyTeam(ICombatParticipant character)
        {
            if (!enemyTeam.Contains(character)) enemyTeam.Add(character);
        }

        public void RemoveFromBattle(ICombatParticipant character)
        {
            playerTeam.Remove(character);
            enemyTeam.Remove(character);
            turnOrder.Remove(character);
        }

        public List<ICombatParticipant> GetPlayerTeam() => playerTeam;
        public List<ICombatParticipant> GetEnemyTeam() => enemyTeam;
        public List<ICombatParticipant> GetAllParticipants() => new List<ICombatParticipant>(playerTeam).Concat(enemyTeam).ToList();
        public List<ICombatParticipant> GetTurnOrder() => turnOrderSystem?.GetTurnOrder() ?? turnOrder;

        /// <summary>
        /// Get the enhanced turn order system
        /// </summary>
        public TurnOrderSystem GetTurnOrderSystem() => turnOrderSystem;

        /// <summary>
        /// Delay a participant's turn
        /// </summary>
        public void DelayTurn(ICombatParticipant participant, int turns = 1)
        {
            turnOrderSystem?.DelayTurn(participant, turns);
        }

        /// <summary>
        /// Speed up a participant's turn
        /// </summary>
        public void SpeedUpTurn(ICombatParticipant participant, int turns = 1)
        {
            turnOrderSystem?.SpeedUpTurn(participant, turns);
        }

        /// <summary>
        /// Skip a participant's next turn
        /// </summary>
        public void SkipNextTurn(ICombatParticipant participant)
        {
            turnOrderSystem?.SkipNextTurn(participant);
        }

        /// <summary>
        /// Add speed modifier to a participant
        /// </summary>
        public void AddSpeedModifier(ICombatParticipant participant, string source, float multiplier)
        {
            turnOrderSystem?.AddSpeedModifier(participant, source, multiplier);
        }

        /// <summary>
        /// Remove speed modifier from a participant
        /// </summary>
        public void RemoveSpeedModifier(ICombatParticipant participant, string source)
        {
            turnOrderSystem?.RemoveSpeedModifier(participant, source);
        }

        public void OnCharacterSelected(ICombatParticipant character)
        {
            Debug.Log($"Selected character: {character.ParticipantName}");
            CharacterSelected?.Invoke(character);
        }

        public void OnCharacterDeselected(ICombatParticipant character)
        {
            CharacterDeselected?.Invoke(character);
        }

        public void UseAbility(ICombatParticipant user, ICombatParticipant target, ICombatAction action)
        {
            if (!IsValidAbilityUse(user, target, action)) return;

            OnActionExecuting?.Invoke(user, target);
            user.TriggerAnimation(action.AnimationTriggerName);
            
            // A small delay for the animation to start before the action logic fires
            StartCoroutine(action.ExecuteCoroutine(user, target, () => 
            {
                // This callback runs after the action is complete
                EndTurn();
            }));

            Debug.Log($"{user.ParticipantName} uses {action.ActionName} on {target.ParticipantName}");
        }

        private bool IsValidAbilityUse(ICombatParticipant user, ICombatParticipant target, ICombatAction action)
        {
            if (user == null || target == null || action == null) return false;
            if (user != currentTurnCharacter) return false;
            if (!user.IsAlive) return false;
            if (!user.CanPerformAction(action)) return false;
            if (!action.IsValidTarget(user, target)) return false;
            return true;
        }



        public void OnPlayerActionSelected()
        {
            _awaitingPlayerInput = false;
            // Player action is complete, move to next turn
            // Note: EndTurn() will be called by UseAbility callback, so we don't call it here
        }

        private IEnumerator<object> AITurn(ICombatParticipant character)
        {
            if (character == null)
            {
                character = GetRandomPlayerCharacter();
                if (character == null)
                {
                    Debug.LogWarning("No player characters available for AI turn");
                    yield break;
                }
            }

            Debug.Log($"--- {character.ParticipantName}'s Turn (AI) ---");

            // Try to use enhanced AI system
            var aiComponent = character.GetTransform()?.GetComponent<TacticalCombatSystem.AI.CombatAI>();

            if (aiComponent != null)
            {
                // Use enhanced AI decision making
                yield return new WaitForSeconds(aiComponent.thinkingTime);

                var decision = aiComponent.MakeDecision();

                if (decision.action != null && decision.target != null)
                {
                    Debug.Log($"{character.ParticipantName} AI Decision: {decision.action.ActionName} on {decision.target.ParticipantName} (Confidence: {decision.confidence:F2})");
                    UseAbility(character, decision.target, decision.action);
                    yield break;
                }
                else
                {
                    Debug.LogWarning($"{character.ParticipantName} AI could not make a decision, falling back to simple AI");
                }
            }

            // Fallback to simple AI
            yield return StartCoroutine(SimpleAITurn(character));
        }

        private IEnumerator<object> SimpleAITurn(ICombatParticipant character)
        {
            var availableActions = character.GetAvailableActions();

            // Find all available attack actions (basic attacks or abilities that target enemies)
            var attackActions = availableActions.Where(a => a.CanTargetEnemies).ToList();

            ICombatAction chosenAction = null;

            if (attackActions.Any())
            {
                // Pick a random attack action
                chosenAction = attackActions[UnityEngine.Random.Range(0, attackActions.Count)];
            }
            else
            {
                // If no attack actions are available, try to find a non-run action
                var nonRunActions = availableActions.Where(a => !(a is RunAction)).ToList();
                if (nonRunActions.Any())
                {
                    chosenAction = nonRunActions[UnityEngine.Random.Range(0, nonRunActions.Count)];
                }
            }

            if (chosenAction == null)
            {
                Debug.Log($"{character.ParticipantName} has no valid actions and ends its turn.");
                StartCoroutine(EndTurnWithDelay(1.0f));
                yield break;
            }

            var target = GetRandomTarget(character, chosenAction.TargetType);
            if (target != null)
            {
                UseAbility(character, target, chosenAction);
            }
            else
            {
                Debug.LogWarning($"{character.ParticipantName} chose {chosenAction.ActionName} but could not find a valid target. Ending turn.");
                StartCoroutine(EndTurnWithDelay(1.0f));
            }
        }

        private IEnumerator<object> EndTurnWithDelay(float delay)
        {
            yield return new WaitForSeconds(delay);
            EndTurn();
        }

        private ICombatParticipant GetRandomPlayerCharacter()
        {
            if (Participants == null) 
                return null;

            var playerCharacters = Participants
                .Where(c => c.IsPlayable)
                .ToList();
            
            if (playerCharacters == null || playerCharacters.Count() == 0)
                return null;
                
            int count = playerCharacters.Count();
            int index = UnityEngine.Random.Range(0, count);
            return playerCharacters[index];
        }

        private ICombatParticipant GetRandomTarget(ICombatParticipant user, TargetType targetType)
        {
            List<ICombatParticipant> potentialTargets = new List<ICombatParticipant>();
            List<ICombatParticipant> userTeam = user.IsPlayerControlled ? playerTeam : enemyTeam;
            List<ICombatParticipant> opposingTeam = user.IsPlayerControlled ? enemyTeam : playerTeam;

            switch (targetType)
            {
                case TargetType.SingleEnemy:
                case TargetType.AllEnemies: // For AI, just pick one from all enemies
                    potentialTargets = opposingTeam.Where(p => p.IsAlive).ToList();
                    break;
                case TargetType.SingleAlly:
                case TargetType.AllAllies: // For AI, just pick one from all allies
                    potentialTargets = userTeam.Where(e => e.IsAlive).ToList();
                    break;
                case TargetType.Self:
                    return user;
            }

            if (potentialTargets.Count > 0)
            {
                return potentialTargets[UnityEngine.Random.Range(0, potentialTargets.Count)];
            }

            return null;
        }

        public void AttemptToRun()
        {
            Debug.Log("Player is attempting to run from battle.");
            // For now, running always succeeds. Future logic for failure chance can go here.
            EndBattle(false); // false = player did not win
        }

        #region Reporter Implementations

        public void ReportDamage(ICombatParticipant target, int amount)
        {
            OnCharacterDamaged?.Invoke(target, amount);
        }

        public void ReportHeal(ICombatParticipant target, int amount)
        {
            OnCharacterHealed?.Invoke(target, amount);
        }

        public void ReportStatusEffect(ICombatParticipant target, IStatusEffect effect)
        {
            OnStatusEffectApplied?.Invoke(target, effect);
        }

        #endregion

        private void HandleTurnStartedCamera(ICombatParticipant character)
        {
            if (_cameraController == null) return;
            if (character.IsPlayerControlled) _cameraController.SwitchToPlayerTurn(character);
            else _cameraController.SwitchToEnemyTurn(character);
        }

        private void HandleActionExecutingCamera(ICombatParticipant user, ICombatParticipant target)
        {
            if (_cameraController == null) return;
            _cameraController.SwitchToActionCamera(user, target);
        }

        private void HandleBattleEndedCamera(bool playerWon)
        {
            if (_cameraController == null) return;
            _cameraController.SwitchToWideShot();
        }
    }
}
