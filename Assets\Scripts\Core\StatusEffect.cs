using UnityEngine;
using System;
using System.Collections.Generic;
using TacticalCombatSystem.Interfaces;
using TacticalCombatSystem.Characters;
using StatType = TacticalCombatSystem.Characters.StatType;
using StatModifier = TacticalCombatSystem.Characters.StatModifier;

namespace TacticalCombatSystem.Core
{
    public enum StatusType
    {
        <PERSON><PERSON>,
        <PERSON>bu<PERSON>,
        <PERSON>un,
        <PERSON><PERSON>,
        Burn,
        Freeze,
        Sleep,
        Silence,
        StatModifier,
        Custom
    }
    
    [CreateAssetMenu(menuName = "Tactical Combat/Status Effect")]
    public class StatusEffect : ScriptableObject, IStatusEffect
    {
        [Header("Basic Info")]
        public string statusName = "New Status";
        [TextArea] public string description = "Status effect description";
        public StatusType statusType = StatusType.Debuff;
        public Sprite icon;
        public Color statusColor = Color.white;
        
        [Header("Duration")]
        public int duration = 3; // Number of turns
        public bool isPermanent = false;
        public bool removeOnCombatEnd = true;
        
        [Header("Visuals")]
        public GameObject visualEffectPrefab;
        public bool showFloatingText = true;
        public string floatingText = "!";

        // IStatusEffect implementation
        public string Name => statusName;
        public string Description => description;
        public Sprite Icon => icon;
        public int Duration { get => currentDuration; set => currentDuration = value; }

        [Header("Stat Modifiers")]
        public List<StatModifier> statModifiers = new List<StatModifier>();

        [Header("Damage Over Time")]
        public bool isDamageOverTime = false;
        public int damagePerTurn = 0;
        public StatType damageScalingStat = StatType.MaxHealth;
        public float damageScalingPercentage = 0.05f; // 5% of max health by default

        [Header("Stacking")]
        public bool canStack = false;
        public int maxStacks = 1;
        public bool refreshOnReapply = true;

        // Events
        public event Action<ICombatParticipant> OnApplied;
        public event Action<ICombatParticipant> OnRemoved;
        public event Action<ICombatParticipant> OnTurnStartEvent;
        public event Action<ICombatParticipant> OnTurnEndEvent;

        // Current state (not serialized)
        [System.NonSerialized] private int currentDuration;
        [System.NonSerialized] private ICombatParticipant target;
        [System.NonSerialized] private GameObject activeVisualEffect;
        [System.NonSerialized] private int currentStacks = 1;
        [System.NonSerialized] private List<StatModifier> appliedModifiers = new List<StatModifier>();

        // For custom behavior
        public Action<ICombatParticipant> OnApplyAction;
        public Action<ICombatParticipant> OnRemoveAction;
        public Action<ICombatParticipant> OnTurnStartCustom;
        public Action<ICombatParticipant> OnTurnEndCustom;
        
        /// <summary>
        /// Initialize the status effect on a target character
        /// </summary>
        public void Apply(ICombatParticipant target)
        {
            OnApply(target);
        }

        public void OnApply(ICombatParticipant target) {
            Initialize(target);
        }

        public void Remove(ICombatParticipant target)
        {
            OnRemove(target);
        }

        public void OnRemove(ICombatParticipant target) {
            RemoveEffect();
        }

        public void OnTurnStart(ICombatParticipant target) {
            OnTurnStartEffect();
        }

        public void OnTurnEnd(ICombatParticipant target) {
            OnTurnEndEffect();
        }

        public IStatusEffect Clone() {
            return Instantiate(this);
        }

        public void Initialize(ICombatParticipant targetCharacter, int customDuration = -1)
        {
            InitializeWithStacks(targetCharacter, customDuration, 1);
        }

        public void InitializeWithStacks(ICombatParticipant targetCharacter, int customDuration = -1, int stacks = 1)
        {
            if (targetCharacter == null) return;

            target = targetCharacter;
            currentDuration = customDuration >= 0 ? customDuration : duration;
            currentStacks = Mathf.Clamp(stacks, 1, maxStacks);

            // Apply the effect
            ApplyEffect();

            // Create visual effect if needed
            if (visualEffectPrefab != null && targetCharacter is MonoBehaviour mb)
            {
                activeVisualEffect = Instantiate(visualEffectPrefab, mb.transform);
                activeVisualEffect.transform.localPosition = Vector3.zero;
            }

            // Show floating text
            if (showFloatingText && !string.IsNullOrEmpty(floatingText))
            {
                // TODO: Show floating text above character
                Debug.Log($"{floatingText} - {targetCharacter.ParticipantName}");
            }

            // Invoke events
            OnApplied?.Invoke(targetCharacter);
            OnApplyAction?.Invoke(targetCharacter);

            string stackText = currentStacks > 1 ? $" (x{currentStacks})" : "";
            Debug.Log($"{statusName}{stackText} applied to {targetCharacter.ParticipantName} for {currentDuration} turns");
        }

        /// <summary>
        /// Try to stack this effect with an existing one
        /// </summary>
        public bool TryStack(StatusEffect existingEffect)
        {
            if (!canStack || existingEffect.statusName != statusName) return false;

            if (refreshOnReapply)
            {
                existingEffect.currentDuration = duration;
            }

            if (existingEffect.currentStacks < maxStacks)
            {
                existingEffect.currentStacks++;
                existingEffect.RefreshStatModifiers();
                Debug.Log($"{statusName} stacked on {existingEffect.target.ParticipantName} (x{existingEffect.currentStacks})");
                return true;
            }

            return false;
        }
        
        /// <summary>
        /// Called at the start of the target's turn
        /// </summary>
        public void OnTurnStartEffect()
        {
            if (target == null || !target.IsAlive) return;

            // Invoke custom turn start behavior
            OnTurnStartEvent?.Invoke(target);
            OnTurnStartCustom?.Invoke(target);

            // Apply damage over time effects
            if (isDamageOverTime || statusType == StatusType.Poison || statusType == StatusType.Burn)
            {
                int damage = CalculateDamageOverTime();
                if (damage > 0)
                {
                    target.OnDamageTaken(damage, target);
                    Debug.Log($"{target.ParticipantName} takes {damage} damage from {statusName}!");
                }
            }

            // Handle stun/sleep effects
            if ((statusType == StatusType.Stun || statusType == StatusType.Sleep) && target is MonoBehaviour)
            {
                // Note: SkipNextTurn functionality would need to be implemented in the character class
                Debug.Log($"{target.ParticipantName} is {statusName.ToLower()}ed and skips their turn!");
            }
        }

        /// <summary>
        /// Calculate damage over time based on settings and stacks
        /// </summary>
        private int CalculateDamageOverTime()
        {
            int baseDamage = damagePerTurn;

            // If using percentage-based damage
            if (damageScalingPercentage > 0f)
            {
                float targetStat = 0f;
                if (target is BaseCharacter baseChar && baseChar.CharacterData != null)
                {
                    targetStat = baseChar.CharacterData.GetModifiedStat(damageScalingStat);
                }
                else
                {
                    // Fallback to basic stats
                    switch (damageScalingStat)
                    {
                        case StatType.MaxHealth: targetStat = target.MaxHP; break;
                        case StatType.MaxMana: targetStat = target.MaxMP; break;
                        default: targetStat = target.MaxHP; break;
                    }
                }

                baseDamage += Mathf.RoundToInt(targetStat * damageScalingPercentage);
            }

            // Apply stacking multiplier
            return baseDamage * currentStacks;
        }
        
        public void OnTurnEndEffect()
        {
            if (target == null || !target.IsAlive) return;
            
            // Invoke custom turn end behavior
            OnTurnEndEvent?.Invoke(target);
            OnTurnEndCustom?.Invoke(target);
            
            // Reduce duration if not permanent
            if (!isPermanent)
            {
                currentDuration--;
                
                // Remove if duration expired
                if (currentDuration <= 0)
                {
                    RemoveEffect();
                }
            }
        }
        
        public void Refresh(int newDuration = -1)
        {
            if (newDuration > 0)
            {
                currentDuration = newDuration;
            }
            else if (!isPermanent)
            {
                currentDuration = Mathf.Max(currentDuration, duration);
            }
            
            Debug.Log($"{statusName} on {target.ParticipantName} refreshed to {currentDuration} turns");
        }
        
        public void RemoveEffect()
        {
            if (target == null) return;

            // Remove stat modifiers first
            RemoveStatModifiers();

            // Clean up visual effect
            if (activeVisualEffect != null)
            {
                Destroy(activeVisualEffect);
            }

            // Remove from target
            if (target != null)
            {
                target.RemoveStatusEffect(this);

                // Invoke removal events
                OnRemoved?.Invoke(target);
                OnRemoveAction?.Invoke(target);

                Debug.Log($"{statusName} removed from {target.ParticipantName}");
            }

            // If this is a runtime-created status effect, destroy it
            if (!string.IsNullOrEmpty(name) && name.StartsWith("New Status"))
            {
                Destroy(this);
            }
        }
        
        protected virtual void ApplyEffect()
        {
            // Apply stat modifiers to the target
            ApplyStatModifiers();
        }

        /// <summary>
        /// Apply stat modifiers to the target character
        /// </summary>
        private void ApplyStatModifiers()
        {
            if (target is BaseCharacter baseChar && baseChar.CharacterData?.Stats != null)
            {
                appliedModifiers.Clear();

                foreach (var modifier in statModifiers)
                {
                    // Create a copy of the modifier with stacking applied
                    var appliedModifier = new StatModifier(
                        modifier.statType,
                        modifier.modifierType,
                        modifier.value * currentStacks,
                        $"StatusEffect:{statusName}",
                        2 // Status effects have priority 2 (higher than equipment)
                    );

                    appliedModifier.isTemporary = true;
                    appliedModifier.duration = currentDuration;

                    baseChar.CharacterData.Stats.AddModifier(appliedModifier);
                    appliedModifiers.Add(appliedModifier);
                }
            }
        }

        /// <summary>
        /// Remove stat modifiers from the target character
        /// </summary>
        private void RemoveStatModifiers()
        {
            if (target is BaseCharacter baseChar && baseChar.CharacterData?.Stats != null)
            {
                baseChar.CharacterData.Stats.RemoveModifiersFromSource($"StatusEffect:{statusName}");
                appliedModifiers.Clear();
            }
        }

        /// <summary>
        /// Refresh stat modifiers (used when stacking changes)
        /// </summary>
        private void RefreshStatModifiers()
        {
            RemoveStatModifiers();
            ApplyStatModifiers();
        }
        
        /// <summary>
        /// Creates a new status effect that modifies a stat.
        /// </summary>
        public static StatusEffect CreateStatModifier(string name, StatType stat, int value, int duration, bool isBuff = true)
        {
            StatusEffect effect = CreateInstance<StatusEffect>();
            effect.statusName = name;
            effect.statusType = StatusType.StatModifier;
            effect.duration = duration;
            effect.isPermanent = duration <= 0;
            effect.statusColor = isBuff ? new Color(0.2f, 0.8f, 0.2f) : new Color(0.8f, 0.2f, 0.2f);
            effect.statModifiers.Add(new StatModifier(stat, StatModifierType.Flat, value, $"StatusEffect:{name}"));
            return effect;
        }

        // Helper method to create a simple status effect
        public static StatusEffect CreateSimpleEffect(
            string name,
            StatusType statusType,
            int duration,
            bool isBuff = true)
        {
            StatusEffect effect = CreateInstance<StatusEffect>();
            effect.statusName = name;
            effect.statusType = statusType;
            effect.duration = duration;
            effect.isPermanent = duration <= 0;
            effect.statusColor = isBuff ? new Color(0.2f, 0.8f, 0.2f) : new Color(0.8f, 0.2f, 0.2f);

            return effect;
        }
    }

    // Note: StatModifier is now defined in Characters/StatType.cs
    // This legacy class is kept for backward compatibility
    [System.Serializable]
    public class LegacyStatModifier
    {
        public string stat; // e.g., "Attack", "Defense"
        public int value;
        public bool isPercentage = false;
    }
}
