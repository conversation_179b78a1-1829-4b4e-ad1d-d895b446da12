# Tactical Combat System - Setup Guide

This guide will help you set up the Tactical Combat System in Unity.

## Prerequisites
- Unity 6000.2.0b2 (beta) or later
- Basic knowledge of Unity Editor

## Setup Steps

### 1. Import the Project
1. Clone or download the repository
2. Open the project in Unity Hub
3. Wait for Unity to import all assets (may take a few minutes)

### 2. Set Up the Scene
1. In the Unity menu, go to `Tactical Combat > Setup Battle Scene`
   - This will create a new scene with all necessary game objects
   - The scene will be saved as `Assets/Scenes/BattleScene.unity`

### 3. Create Character Prefabs
1. In the Unity menu, go to `Tactical Combat > Create Character Prefab`
   - This will create a new character prefab in the `Assets/Prefabs/Characters` folder
   - Repeat this step for each character type you want to create

### 4. Set Up Test Data
1. In the Unity menu, go to `Tactical Combat > Setup Test Data`
   - This will create sample characters, abilities, and status effects in the `Assets/Data` folder

### 5. Configure the BattleInitializer
1. In the Unity scene, select the `BattleManager` game object
2. In the Inspector, locate the `Battle Initializer` component. The `Setup Battle Scene` tool will have already linked the core managers and UI.
3. You only need to assign the following:
   - `Player Prefabs`: Add your player character prefabs
   - `Enemy Prefabs`: Add your enemy character prefabs
   - `Player Spawn Points` and `Enemy Spawn Points` are already assigned by the setup tool, but you can customize them.

### 6. Test the Battle System
1. Click the Play button in Unity
2. Use the mouse to select characters and abilities
3. The battle should proceed with turns, damage numbers, and status effects

## Troubleshooting

### Common Issues

#### Missing Script References
- If you see "Missing (Script)" in the Inspector:
  1. Make sure all script files are in the correct folders
  2. Check for compilation errors in the Console window
  3. Try reimporting the scripts (right-click in Project window > Reimport)

#### UI Not Appearing
- Ensure the Canvas is set to "Screen Space - Overlay"
- Check that the UI elements are not hidden behind other objects
- Verify that the UI elements have proper layout components

#### Characters Not Spawning
- Check that spawn points are assigned in the BattleInitializer
- Verify that character prefabs have the required components:
  - CharacterVisual
  - CharacterUI
  - Collider
  - Rigidbody (with isKinematic = true)

#### Abilities Not Working
- Verify that ability ScriptableObjects are properly configured
- Check that the Ability and AbilityEffect scripts are properly assigned
- Ensure the target selection is working correctly

## Next Steps
- Create more character classes and abilities
- Implement a progression system (leveling, equipment)
- Add special effects and animations
- Create a more sophisticated AI
- Add a main menu and game flow

## Support
If you encounter any issues, please check the following:
1. Unity Console for error messages
2. That all required components are properly assigned
3. That all ScriptableObjects are created and assigned correctly

For additional help, please refer to the project's documentation or contact the development team.
