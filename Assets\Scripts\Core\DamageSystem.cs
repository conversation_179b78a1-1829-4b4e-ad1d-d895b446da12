using System;
using System.Collections.Generic;
using UnityEngine;
using TacticalCombatSystem.Interfaces;
using TacticalCombatSystem.Characters;
using TacticalCombatSystem.Actions;
using StatType = TacticalCombatSystem.Characters.StatType;

namespace TacticalCombatSystem.Core
{
    /// <summary>
    /// Advanced damage calculation system with elemental types, resistances, and critical hits
    /// </summary>
    public static class DamageSystem
    {
        /// <summary>
        /// Calculate damage with all modifiers applied
        /// </summary>
        public static DamageResult CalculateDamage(DamageCalculationData data)
        {
            var result = new DamageResult
            {
                damageType = data.damageType,
                element = data.elementType,
                baseDamage = data.baseDamage
            };
            
            float finalDamage = data.baseDamage;
            
            // Apply damage variance
            if (data.damageVariance > 0f)
            {
                float variance = UnityEngine.Random.Range(-data.damageVariance, data.damageVariance);
                finalDamage *= (1f + variance);
            }
            
            // Add flat damage bonuses
            finalDamage += data.flatDamageBonus;
            
            // Apply attacker's stats
            finalDamage += GetAttackStat(data.attacker, data.damageType);
            
            // Apply weapon damage if applicable
            finalDamage += GetWeaponDamage(data.attacker, data.damageType);
            
            // Apply percentage damage bonuses
            finalDamage *= (1f + data.percentageDamageBonus);
            
            // Check for critical hit
            result.isCritical = CheckCriticalHit(data.attacker, data.target, data.baseCritChance);
            if (result.isCritical)
            {
                float critMultiplier = GetCriticalMultiplier(data.attacker);
                finalDamage *= critMultiplier;
                result.criticalMultiplier = critMultiplier;
            }
            
            // Apply elemental effectiveness
            float elementalMultiplier = GetElementalEffectiveness(data.elementType, data.target);
            finalDamage *= elementalMultiplier;
            result.elementalMultiplier = elementalMultiplier;
            
            // Apply target's defense
            if (!data.ignoreDefense)
            {
                float defense = GetDefenseStat(data.target, data.damageType);
                float defenseReduction = CalculateDefenseReduction(defense, data.armorPenetration);
                finalDamage *= defenseReduction;
                result.defenseReduction = 1f - defenseReduction;
            }
            
            // Apply damage reduction effects (like defending)
            if (data.target.IsDefending)
            {
                finalDamage *= data.defendingReduction;
                result.wasDefending = true;
            }
            
            // Apply final damage modifiers
            finalDamage *= data.finalDamageMultiplier;
            
            // Ensure minimum damage
            result.damage = Mathf.Max(data.minimumDamage, Mathf.RoundToInt(finalDamage));
            
            return result;
        }
        
        /// <summary>
        /// Calculate healing with all modifiers applied
        /// </summary>
        public static HealingResult CalculateHealing(HealingCalculationData data)
        {
            var result = new HealingResult
            {
                baseHealing = data.baseHealing
            };
            
            float finalHealing = data.baseHealing;
            
            // Apply healing variance
            if (data.healingVariance > 0f)
            {
                float variance = UnityEngine.Random.Range(-data.healingVariance, data.healingVariance);
                finalHealing *= (1f + variance);
            }
            
            // Add flat healing bonuses
            finalHealing += data.flatHealingBonus;
            
            // Apply healer's stats
            if (data.scaleWithMagicAttack)
            {
                float magicAttack = GetMagicAttackStat(data.healer);
                finalHealing += magicAttack * data.magicAttackScaling;
            }
            
            // Apply percentage healing bonuses
            finalHealing *= (1f + data.percentageHealingBonus);
            
            // Check for critical healing
            result.isCritical = CheckCriticalHeal(data.healer, data.baseCritChance);
            if (result.isCritical)
            {
                float critMultiplier = GetCriticalHealMultiplier(data.healer);
                finalHealing *= critMultiplier;
                result.criticalMultiplier = critMultiplier;
            }
            
            // Apply target's healing received modifiers
            finalHealing *= GetHealingReceivedMultiplier(data.target);
            
            // Apply final healing modifiers
            finalHealing *= data.finalHealingMultiplier;
            
            // Calculate actual healing (can't exceed missing health)
            int currentHP = data.target.CurrentHP;
            int maxHP = data.target.MaxHP;
            int missingHP = maxHP - currentHP;
            
            result.healing = Mathf.Min(missingHP, Mathf.RoundToInt(finalHealing));
            result.overheal = Mathf.Max(0, Mathf.RoundToInt(finalHealing) - result.healing);
            
            return result;
        }
        
        /// <summary>
        /// Get attack stat based on damage type
        /// </summary>
        private static float GetAttackStat(ICombatParticipant attacker, DamageType damageType)
        {
            if (attacker is BaseCharacter baseChar && baseChar.CharacterData != null)
            {
                return damageType switch
                {
                    DamageType.Physical => baseChar.CharacterData.GetModifiedStat(StatType.PhysicalAttack),
                    DamageType.Magical => baseChar.CharacterData.GetModifiedStat(StatType.MagicAttack),
                    DamageType.True => 0f,
                    _ => baseChar.CharacterData.GetModifiedStat(StatType.PhysicalAttack)
                };
            }
            
            return damageType switch
            {
                DamageType.Physical => attacker.Attack,
                DamageType.Magical => attacker.MagicAttack,
                DamageType.True => 0f,
                _ => attacker.Attack
            };
        }
        
        /// <summary>
        /// Get magic attack stat for healing calculations
        /// </summary>
        private static float GetMagicAttackStat(ICombatParticipant healer)
        {
            if (healer is BaseCharacter baseChar && baseChar.CharacterData != null)
            {
                return baseChar.CharacterData.GetModifiedStat(StatType.MagicAttack);
            }
            
            return healer.MagicAttack;
        }
        
        /// <summary>
        /// Get weapon damage bonus
        /// </summary>
        private static float GetWeaponDamage(ICombatParticipant attacker, DamageType damageType)
        {
            if (attacker is BaseCharacter baseChar && baseChar.CharacterData?.Equipment != null)
            {
                var weapon = baseChar.CharacterData.Equipment.GetEquippedItem(Equipment.EquipmentSlot.Weapon);
                if (weapon != null && weapon.equipmentType == Equipment.EquipmentType.Weapon)
                {
                    return weapon.CalculateDamage();
                }
            }
            
            return 0f;
        }
        
        /// <summary>
        /// Get defense stat based on damage type
        /// </summary>
        private static float GetDefenseStat(ICombatParticipant target, DamageType damageType)
        {
            if (target is BaseCharacter baseChar && baseChar.CharacterData != null)
            {
                return damageType switch
                {
                    DamageType.Physical => baseChar.CharacterData.GetModifiedStat(StatType.PhysicalDefense),
                    DamageType.Magical => baseChar.CharacterData.GetModifiedStat(StatType.MagicDefense),
                    DamageType.True => 0f,
                    _ => baseChar.CharacterData.GetModifiedStat(StatType.PhysicalDefense)
                };
            }
            
            return damageType switch
            {
                DamageType.Physical => target.Defense,
                DamageType.Magical => target.MagicDefense,
                DamageType.True => 0f,
                _ => target.Defense
            };
        }
        
        /// <summary>
        /// Calculate defense reduction based on defense stat and armor penetration
        /// </summary>
        private static float CalculateDefenseReduction(float defense, float armorPenetration)
        {
            // Apply armor penetration
            float effectiveDefense = Mathf.Max(0f, defense - armorPenetration);
            
            // Defense formula: damage multiplier = 100 / (100 + defense)
            return 100f / (100f + effectiveDefense);
        }
        
        /// <summary>
        /// Check for critical hit
        /// </summary>
        private static bool CheckCriticalHit(ICombatParticipant attacker, ICombatParticipant target, float baseCritChance)
        {
            float critChance = baseCritChance;
            
            if (attacker is BaseCharacter baseChar && baseChar.CharacterData != null)
            {
                critChance = baseChar.CharacterData.GetModifiedStat(StatType.CriticalChance);
            }
            
            // Apply target's crit resistance if any
            // This could be implemented as a stat modifier
            
            return UnityEngine.Random.value <= critChance;
        }
        
        /// <summary>
        /// Check for critical healing
        /// </summary>
        private static bool CheckCriticalHeal(ICombatParticipant healer, float baseCritChance)
        {
            float critChance = baseCritChance;
            
            if (healer is BaseCharacter baseChar && baseChar.CharacterData != null)
            {
                critChance = baseChar.CharacterData.GetModifiedStat(StatType.CriticalChance) * 0.5f; // Half crit chance for healing
            }
            
            return UnityEngine.Random.value <= critChance;
        }
        
        /// <summary>
        /// Get critical damage multiplier
        /// </summary>
        private static float GetCriticalMultiplier(ICombatParticipant attacker)
        {
            if (attacker is BaseCharacter baseChar && baseChar.CharacterData != null)
            {
                return baseChar.CharacterData.GetModifiedStat(StatType.CriticalDamage);
            }
            
            return 1.5f; // Default 150% damage
        }
        
        /// <summary>
        /// Get critical healing multiplier
        /// </summary>
        private static float GetCriticalHealMultiplier(ICombatParticipant healer)
        {
            if (healer is BaseCharacter baseChar && baseChar.CharacterData != null)
            {
                return baseChar.CharacterData.GetModifiedStat(StatType.CriticalDamage) * 0.8f; // Reduced for healing
            }
            
            return 1.3f; // Default 130% healing
        }
        
        /// <summary>
        /// Get elemental effectiveness multiplier
        /// </summary>
        private static float GetElementalEffectiveness(ElementType elementType, ICombatParticipant target)
        {
            if (elementType == ElementType.None) return 1f;
            
            if (target is BaseCharacter baseChar && baseChar.CharacterData != null)
            {
                StatType resistanceStat = elementType switch
                {
                    ElementType.Fire => StatType.FireResistance,
                    ElementType.Ice => StatType.IceResistance,
                    ElementType.Lightning => StatType.LightningResistance,
                    ElementType.Earth => StatType.EarthResistance,
                    ElementType.Wind => StatType.WindResistance,
                    ElementType.Water => StatType.WaterResistance,
                    ElementType.Light => StatType.LightResistance,
                    ElementType.Dark => StatType.DarkResistance,
                    _ => StatType.PhysicalDefense
                };
                
                float resistance = baseChar.CharacterData.GetModifiedStat(resistanceStat);
                
                // Convert resistance to damage multiplier
                // 0 resistance = 1.0x damage, 0.5 resistance = 0.5x damage, -0.5 resistance = 1.5x damage
                return Mathf.Clamp(1f - resistance, 0.1f, 3f); // 10% minimum, 300% maximum
            }
            
            return 1f;
        }
        
        /// <summary>
        /// Get healing received multiplier for target
        /// </summary>
        private static float GetHealingReceivedMultiplier(ICombatParticipant target)
        {
            // This could be implemented as status effects or equipment bonuses
            // For now, return 1.0 (no modifier)
            return 1f;
        }
    }
    
    /// <summary>
    /// Data structure for damage calculations
    /// </summary>
    [Serializable]
    public struct DamageCalculationData
    {
        public ICombatParticipant attacker;
        public ICombatParticipant target;
        public float baseDamage;
        public DamageType damageType;
        public ElementType elementType;
        public float damageVariance;
        public float flatDamageBonus;
        public float percentageDamageBonus;
        public float baseCritChance;
        public float armorPenetration;
        public bool ignoreDefense;
        public float defendingReduction;
        public float finalDamageMultiplier;
        public int minimumDamage;
    }
    
    /// <summary>
    /// Data structure for healing calculations
    /// </summary>
    [Serializable]
    public struct HealingCalculationData
    {
        public ICombatParticipant healer;
        public ICombatParticipant target;
        public float baseHealing;
        public float healingVariance;
        public float flatHealingBonus;
        public float percentageHealingBonus;
        public bool scaleWithMagicAttack;
        public float magicAttackScaling;
        public float baseCritChance;
        public float finalHealingMultiplier;
    }
    
    /// <summary>
    /// Result of damage calculation
    /// </summary>
    [Serializable]
    public struct DamageResult
    {
        public int damage;
        public float baseDamage;
        public bool isCritical;
        public float criticalMultiplier;
        public DamageType damageType;
        public ElementType element;
        public float elementalMultiplier;
        public float defenseReduction;
        public bool wasDefending;
    }
    
    /// <summary>
    /// Result of healing calculation
    /// </summary>
    [Serializable]
    public struct HealingResult
    {
        public int healing;
        public float baseHealing;
        public int overheal;
        public bool isCritical;
        public float criticalMultiplier;
    }
}
