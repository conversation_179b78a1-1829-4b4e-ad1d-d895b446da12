using UnityEngine;
using UnityEngine.UI;
using UnityEngine.EventSystems;
using System.Collections.Generic;
using System.Linq;

namespace TacticalCombatSystem.UI
{
    /// <summary>
    /// Manages a group of UI Selectables for keyboard/controller navigation.
    /// Automatically selects the first active element when the GameObject is enabled.
    /// </summary>
    [RequireComponent(typeof(RectTransform))]
    public class SelectableGroup : MonoBehaviour
    {
        [Tooltip("The UI elements that can be navigated in this group. Order matters for fallback selection.")]
        public List<Selectable> selectables = new List<Selectable>();

        [Tooltip("The element to be selected by default when this group becomes active. If null, the first element in the list is used.")]
        public Selectable firstSelected;

        private void OnEnable()
        {
            // A small delay to ensure the UI is fully active and visible before selecting.
            StartCoroutine(SelectFirstElementAfterFrame());
        }

        private System.Collections.IEnumerator SelectFirstElementAfterFrame()
        {
            yield return null; // Wait for one frame for the UI to stabilize.

            Selectable elementToSelect = firstSelected;

            // If no specific first element is set, or if it's not interactable, find the first available one.
            if (elementToSelect == null || !elementToSelect.IsInteractable())
            {
                elementToSelect = selectables.FirstOrDefault(s => s != null && s.IsInteractable());
            }
            
            if (elementToSelect != null)
            {
                EventSystem.current.SetSelectedGameObject(elementToSelect.gameObject);
            }
        }
    }
}