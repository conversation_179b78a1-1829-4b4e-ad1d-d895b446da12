using UnityEditor;
using UnityEngine;
using UnityEngine.UI;
using TMPro;
using TacticalCombatSystem.Core;
using TacticalCombatSystem.Battle;
using TacticalCombatSystem.UI;
using System.Collections.Generic;
using System.Linq;
using UnityEngine.EventSystems;

public class SceneSetupHelper
{
    [MenuItem("Tactical Combat/Setup Battle Scene")]
    public static void SetupBattleScene()
    {
        // Create a new scene or clear the existing one
        var scene = UnityEditor.SceneManagement.EditorSceneManager.NewScene(UnityEditor.SceneManagement.NewSceneSetup.DefaultGameObjects, UnityEditor.SceneManagement.NewSceneMode.Single);

        // Create core manager objects
        var managers = new GameObject("Managers");
        var battleManager = managers.AddComponent<BattleManager>();
        var battleInitializer = managers.AddComponent<BattleInitializer>(); // Adds the RUNTIME script
        var inputHandler = managers.AddComponent<BattleInputHandler>();


        // Create UI
        var battleCanvas = CreateBattleUI(managers.transform);
        var battleUI = battleCanvas.GetComponent<BattleUI>();

        // Link references in BattleInitializer
        battleInitializer.battleManager = battleManager;
        battleInitializer.battleUI = battleUI;
        battleInitializer.inputHandler = inputHandler;
        battleInitializer.battleCamera = UnityEngine.Camera.main;

        // Link BattleUI to InputHandler
        inputHandler.battleUI = battleUI;

        // Create Spawn Points
        var spawnPointsRoot = new GameObject("SpawnPoints");
        var playerSpawns = new GameObject("PlayerSpawns");
        playerSpawns.transform.SetParent(spawnPointsRoot.transform);
        var enemySpawns = new GameObject("EnemySpawns");
        enemySpawns.transform.SetParent(spawnPointsRoot.transform);

        // Create some default spawn points and link them to the initializer
        var playerSpawnTransforms = new Transform[4];
        var enemySpawnTransforms = new Transform[4];
        for (int i = 0; i < 4; i++)
        {
            var pSpawn = new GameObject($"PlayerSpawn_{i + 1}");
            pSpawn.transform.SetParent(playerSpawns.transform);
            pSpawn.transform.position = new Vector3(-5, 0, -2.25f + (i * 1.5f));
            playerSpawnTransforms[i] = pSpawn.transform;

            var eSpawn = new GameObject($"EnemySpawn_{i + 1}");
            eSpawn.transform.SetParent(enemySpawns.transform);
            eSpawn.transform.position = new Vector3(5, 0, -2.25f + (i * 1.5f));
            enemySpawnTransforms[i] = eSpawn.transform;
        }
        battleInitializer.playerSpawnPoints = playerSpawnTransforms;
        battleInitializer.enemySpawnPoints = enemySpawnTransforms;

        // Select the BattleInitializer so the user can immediately assign prefabs
        Selection.activeGameObject = managers;
        EditorGUIUtility.PingObject(managers);

        Debug.Log("Battle Scene setup complete. Assign character prefabs to the BattleInitializer component on the 'Managers' GameObject.");
    }

    private static GameObject CreateBattleUI(Transform parent)
    {
        var battleCanvasObj = new GameObject("BattleCanvas");
        battleCanvasObj.transform.SetParent(parent);
        var canvas = battleCanvasObj.AddComponent<Canvas>();
        canvas.renderMode = RenderMode.ScreenSpaceOverlay;
        battleCanvasObj.AddComponent<CanvasScaler>();
        battleCanvasObj.AddComponent<GraphicRaycaster>();

        var battleUIComponent = battleCanvasObj.AddComponent<BattleUI>();

        // Create and link UI panels
        battleUIComponent.characterInfoPanel = CreateUIPanel(battleCanvasObj.transform, "CharacterInfoPanel");
        battleUIComponent.abilityPanel = CreateUIPanel(battleCanvasObj.transform, "AbilityPanel");
        battleUIComponent.actionButtonsPanel = CreateUIPanel(battleCanvasObj.transform, "ActionButtonsPanel");
        battleUIComponent.targetSelectionPanel = CreateUIPanel(battleCanvasObj.transform, "TargetSelectionPanel");
        battleUIComponent.battleLogPanel = CreateUIPanel(battleCanvasObj.transform, "BattleLogPanel");

        // --- Setup Action Buttons Panel ---
        var actionPanelGroup = battleUIComponent.actionButtonsPanel.gameObject.AddComponent<SelectableGroup>();
        var actionButtons = new List<Selectable>();

        // Create example buttons. Their onClick events will need to be wired up in the inspector or by code.
        var attackButton = CreateButton(battleUIComponent.actionButtonsPanel, "Attack", new Vector2(0, 60));
        attackButton.onClick.AddListener(battleUIComponent.OnAttackActionSelected);
        actionButtons.Add(attackButton);

        var abilitiesButton = CreateButton(battleUIComponent.actionButtonsPanel, "Abilities", new Vector2(0, 20));
        abilitiesButton.onClick.AddListener(battleUIComponent.OnAbilitiesActionSelected);
        actionButtons.Add(abilitiesButton);

        var itemsButton = CreateButton(battleUIComponent.actionButtonsPanel, "Items", new Vector2(0, -20));
        itemsButton.onClick.AddListener(battleUIComponent.OnItemsActionSelected);
        actionButtons.Add(itemsButton);

        var defendButton = CreateButton(battleUIComponent.actionButtonsPanel, "Defend", new Vector2(0, -60));
        defendButton.onClick.AddListener(battleUIComponent.OnDefendActionSelected);
        actionButtons.Add(defendButton);

        // Assign buttons to the group
        actionPanelGroup.selectables = actionButtons;
        actionPanelGroup.firstSelected = attackButton; // 'Attack' will be selected by default.

        battleUIComponent.abilityPanel.gameObject.AddComponent<SelectableGroup>();

        // Create the battle log text and link it
        var logTextObj = new GameObject("BattleLogText", typeof(TextMeshProUGUI));
        logTextObj.transform.SetParent(battleUIComponent.battleLogPanel, false);
        battleUIComponent.battleLogText = logTextObj.GetComponent<TextMeshProUGUI>();
        // Basic setup for the log text
        var logTextRect = logTextObj.GetComponent<RectTransform>();
        logTextRect.anchorMin = Vector2.zero;
        logTextRect.anchorMax = Vector2.one;
        logTextRect.offsetMin = new Vector2(10, 10);
        logTextRect.offsetMax = new Vector2(-10, -10);

        // Deactivate panels by default
        battleUIComponent.characterInfoPanel.gameObject.SetActive(false);
        battleUIComponent.abilityPanel.gameObject.SetActive(false);
        battleUIComponent.actionButtonsPanel.gameObject.SetActive(false);
        battleUIComponent.targetSelectionPanel.gameObject.SetActive(false);

        return battleCanvasObj;
    }

    private static RectTransform CreateUIPanel(Transform parent, string name)
    {
        var panelObj = new GameObject(name, typeof(RectTransform));
        panelObj.transform.SetParent(parent, false);
        var rectTransform = panelObj.GetComponent<RectTransform>();
        rectTransform.anchorMin = Vector2.zero;
        rectTransform.anchorMax = Vector2.one;
        rectTransform.offsetMin = Vector2.zero;
        rectTransform.offsetMax = Vector2.zero;
        return rectTransform;
    }

    private static Button CreateButton(RectTransform parent, string text, Vector2 position)
    {
        var buttonGO = new GameObject(text + "Button", typeof(Image), typeof(Button));
        buttonGO.transform.SetParent(parent, false);

        var buttonRect = buttonGO.GetComponent<RectTransform>();
        buttonRect.sizeDelta = new Vector2(160, 30);
        buttonRect.anchoredPosition = position;

        var buttonImg = buttonGO.GetComponent<Image>();
        buttonImg.color = new Color(0.1f, 0.1f, 0.1f, 0.7f);

        var buttonComp = buttonGO.GetComponent<Button>();
        var colors = buttonComp.colors;
        colors.highlightedColor = new Color(0.9f, 0.85f, 0.4f);
        colors.selectedColor = new Color(0.9f, 0.85f, 0.4f);
        buttonComp.colors = colors;

        // Set navigation to automatic, which is great for simple vertical/horizontal layouts.
        var nav = buttonComp.navigation;
        nav.mode = Navigation.Mode.Automatic;
        buttonComp.navigation = nav;

        var textGO = new GameObject("Text", typeof(TextMeshProUGUI));
        textGO.transform.SetParent(buttonRect, false);
        var textComp = textGO.GetComponent<TextMeshProUGUI>();
        textComp.text = text;
        textComp.alignment = TextAlignmentOptions.Center;
        textComp.fontSize = 20;
        var textRect = textGO.GetComponent<RectTransform>();
        textRect.anchorMin = Vector2.zero;
        textRect.anchorMax = Vector2.one;
        textRect.offsetMin = Vector2.zero;
        textRect.offsetMax = Vector2.zero;

        return buttonComp;
    }
}
