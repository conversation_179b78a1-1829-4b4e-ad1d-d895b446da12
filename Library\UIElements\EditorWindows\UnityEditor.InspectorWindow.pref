%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &1
MonoBehaviour:
  m_ObjectHideFlags: 61
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 12386, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: 
  m_EditorClassIdentifier: UnityEditor.dll::UnityEditor.UIElements.SerializableJsonDictionary
  m_Keys:
  - __PanelContainer__rootVisualContainer__inspector-window-main-scroll-view__unity-list-statusEffects__UnityEngine.UIElements.ListView
  - __PanelContainer__rootVisualContainer__inspector-window-main-scroll-view__unity-list-statusEffects__unity-vertical-collection-scroll-view__UnityEngine.UIElements.ScrollView
  - __PanelContainer__rootVisualContainer__inspector-window-main-scroll-view__unity-list-statusEffects__unity-vertical-collection-scroll-view__Slider__UnityEngine.UIElements.Scroller+ScrollerSlider
  - __PanelContainer__rootVisualContainer__inspector-window-main-scroll-view__unity-list-startingAbilities__UnityEngine.UIElements.ListView
  - __PanelContainer__rootVisualContainer__inspector-window-main-scroll-view__unity-list-startingAbilities__unity-vertical-collection-scroll-view__UnityEngine.UIElements.ScrollView
  - __PanelContainer__rootVisualContainer__inspector-window-main-scroll-view__unity-list-startingAbilities__unity-vertical-collection-scroll-view__Slider__UnityEngine.UIElements.Scroller+ScrollerSlider
  - __PanelContainer__rootVisualContainer__inspector-window-main-scroll-view__unity-list-abilities__UnityEngine.UIElements.ListView
  - __PanelContainer__rootVisualContainer__inspector-window-main-scroll-view__unity-list-abilities__unity-vertical-collection-scroll-view__UnityEngine.UIElements.ScrollView
  - __PanelContainer__rootVisualContainer__inspector-window-main-scroll-view__unity-list-abilities__unity-vertical-collection-scroll-view__Slider__UnityEngine.UIElements.Scroller+ScrollerSlider
  - __PanelContainer__rootVisualContainer__inspector-window-main-scroll-view__unity-list-startingStatusEffects__UnityEngine.UIElements.ListView
  - __PanelContainer__rootVisualContainer__inspector-window-main-scroll-view__unity-list-startingStatusEffects__unity-vertical-collection-scroll-view__UnityEngine.UIElements.ScrollView
  - __PanelContainer__rootVisualContainer__inspector-window-main-scroll-view__unity-list-startingStatusEffects__unity-vertical-collection-scroll-view__Slider__UnityEngine.UIElements.Scroller+ScrollerSlider
  - __PanelContainer__rootVisualContainer__inspector-window-main-scroll-view__m_ScriptableObjects__UnityEngine.UIElements.ListView
  - __PanelContainer__rootVisualContainer__inspector-window-main-scroll-view__m_ScriptableObjects__unity-vertical-collection-scroll-view__UnityEngine.UIElements.ScrollView
  - __PanelContainer__rootVisualContainer__inspector-window-main-scroll-view__m_ScriptableObjects__unity-vertical-collection-scroll-view__Slider__UnityEngine.UIElements.Scroller+ScrollerSlider
  - __PanelContainer__rootVisualContainer__inspector-window-main-scroll-view__unity-list-availableActions__UnityEngine.UIElements.ListView
  - __PanelContainer__rootVisualContainer__inspector-window-main-scroll-view__unity-list-availableActions__unity-vertical-collection-scroll-view__UnityEngine.UIElements.ScrollView
  - __PanelContainer__rootVisualContainer__inspector-window-main-scroll-view__unity-list-availableActions__unity-vertical-collection-scroll-view__Slider__UnityEngine.UIElements.Scroller+ScrollerSlider
  - __PanelContainer__rootVisualContainer__inspector-window-main-scroll-view__unity-list-testPlayerTeam__UnityEngine.UIElements.ListView
  - __PanelContainer__rootVisualContainer__inspector-window-main-scroll-view__unity-list-testPlayerTeam__unity-vertical-collection-scroll-view__UnityEngine.UIElements.ScrollView
  - __PanelContainer__rootVisualContainer__inspector-window-main-scroll-view__unity-list-testPlayerTeam__unity-vertical-collection-scroll-view__Slider__UnityEngine.UIElements.Scroller+ScrollerSlider
  - __PanelContainer__rootVisualContainer__inspector-window-main-scroll-view__unity-list-testEnemyTeam__UnityEngine.UIElements.ListView
  - __PanelContainer__rootVisualContainer__inspector-window-main-scroll-view__unity-list-testEnemyTeam__unity-vertical-collection-scroll-view__UnityEngine.UIElements.ScrollView
  - __PanelContainer__rootVisualContainer__inspector-window-main-scroll-view__unity-list-testEnemyTeam__unity-vertical-collection-scroll-view__Slider__UnityEngine.UIElements.Scroller+ScrollerSlider
  - __PanelContainer__rootVisualContainer__inspector-window-main-scroll-view__unity-vertical-collection-scroll-view__UnityEngine.UIElements.ScrollView
  - __PanelContainer__rootVisualContainer__inspector-window-main-scroll-view__unity-vertical-collection-scroll-view__Slider__UnityEngine.UIElements.Scroller+ScrollerSlider
  - __PanelContainer__rootVisualContainer__inspector-window-main-scroll-view__unity-list-playerPrefabs__UnityEngine.UIElements.ListView
  - __PanelContainer__rootVisualContainer__inspector-window-main-scroll-view__unity-list-playerPrefabs__unity-vertical-collection-scroll-view__UnityEngine.UIElements.ScrollView
  - __PanelContainer__rootVisualContainer__inspector-window-main-scroll-view__unity-list-playerPrefabs__unity-vertical-collection-scroll-view__Slider__UnityEngine.UIElements.Scroller+ScrollerSlider
  - __PanelContainer__rootVisualContainer__inspector-window-main-scroll-view__unity-list-enemyPrefabs__UnityEngine.UIElements.ListView
  - __PanelContainer__rootVisualContainer__inspector-window-main-scroll-view__unity-list-enemyPrefabs__unity-vertical-collection-scroll-view__UnityEngine.UIElements.ScrollView
  - __PanelContainer__rootVisualContainer__inspector-window-main-scroll-view__unity-list-enemyPrefabs__unity-vertical-collection-scroll-view__Slider__UnityEngine.UIElements.Scroller+ScrollerSlider
  - __PanelContainer__rootVisualContainer__inspector-window-main-scroll-view__unity-list-playerSpawnPoints__UnityEngine.UIElements.ListView
  - __PanelContainer__rootVisualContainer__inspector-window-main-scroll-view__unity-list-playerSpawnPoints__unity-vertical-collection-scroll-view__UnityEngine.UIElements.ScrollView
  - __PanelContainer__rootVisualContainer__inspector-window-main-scroll-view__unity-list-playerSpawnPoints__unity-vertical-collection-scroll-view__Slider__UnityEngine.UIElements.Scroller+ScrollerSlider
  - __PanelContainer__rootVisualContainer__inspector-window-main-scroll-view__unity-list-enemySpawnPoints__UnityEngine.UIElements.ListView
  - __PanelContainer__rootVisualContainer__inspector-window-main-scroll-view__unity-list-enemySpawnPoints__unity-vertical-collection-scroll-view__UnityEngine.UIElements.ScrollView
  - __PanelContainer__rootVisualContainer__inspector-window-main-scroll-view__unity-list-enemySpawnPoints__unity-vertical-collection-scroll-view__Slider__UnityEngine.UIElements.Scroller+ScrollerSlider
  - __PanelContainer__rootVisualContainer__inspector-window-main-scroll-view__unity-list-testPlayerCharacters__UnityEngine.UIElements.ListView
  - __PanelContainer__rootVisualContainer__inspector-window-main-scroll-view__unity-list-testPlayerCharacters__unity-vertical-collection-scroll-view__UnityEngine.UIElements.ScrollView
  - __PanelContainer__rootVisualContainer__inspector-window-main-scroll-view__unity-list-testPlayerCharacters__unity-vertical-collection-scroll-view__Slider__UnityEngine.UIElements.Scroller+ScrollerSlider
  - __PanelContainer__rootVisualContainer__inspector-window-main-scroll-view__unity-list-testEnemyCharacters__UnityEngine.UIElements.ListView
  - __PanelContainer__rootVisualContainer__inspector-window-main-scroll-view__unity-list-testEnemyCharacters__unity-vertical-collection-scroll-view__UnityEngine.UIElements.ScrollView
  - __PanelContainer__rootVisualContainer__inspector-window-main-scroll-view__unity-list-testEnemyCharacters__unity-vertical-collection-scroll-view__Slider__UnityEngine.UIElements.Scroller+ScrollerSlider
  - __PanelContainer__rootVisualContainer__inspector-window-main-scroll-view__unity-list-effects__UnityEngine.UIElements.ListView
  - __PanelContainer__rootVisualContainer__inspector-window-main-scroll-view__unity-list-effects__unity-vertical-collection-scroll-view__UnityEngine.UIElements.ScrollView
  - __PanelContainer__rootVisualContainer__inspector-window-main-scroll-view__unity-list-effects__unity-vertical-collection-scroll-view__Slider__UnityEngine.UIElements.Scroller+ScrollerSlider
  m_Values:
  - '{}'
  - '{}'
  - '{}'
  - '{}'
  - '{}'
  - '{}'
  - '{}'
  - '{}'
  - '{}'
  - '{}'
  - '{}'
  - '{}'
  - '{}'
  - '{}'
  - '{}'
  - '{}'
  - '{}'
  - '{}'
  - '{}'
  - '{}'
  - '{}'
  - '{}'
  - '{}'
  - '{}'
  - '{}'
  - '{}'
  - '{"m_ShowAlternatingRowBackgrounds":0,"serializedVirtualizationData":{"scrollOffset":{"x":0.0,"y":0.0},"firstVisibleIndex":0,"contentPadding":0.0,"contentHeight":22.0,"anchoredItemIndex":0,"anchorOffset":0.0},"m_SelectedIds":[0]}'
  - '{"m_ScrollOffset":{"x":0.0,"y":0.0}}'
  - '{"m_Value":0.0,"m_LowValue":0.0,"m_HighValue":0.0}'
  - '{"m_ShowAlternatingRowBackgrounds":0,"serializedVirtualizationData":{"scrollOffset":{"x":0.0,"y":0.0},"firstVisibleIndex":0,"contentPadding":0.0,"contentHeight":22.0,"anchoredItemIndex":0,"anchorOffset":0.0},"m_SelectedIds":[0]}'
  - '{"m_ScrollOffset":{"x":0.0,"y":0.0}}'
  - '{"m_Value":0.0,"m_LowValue":0.0,"m_HighValue":0.0}'
  - '{"m_ShowAlternatingRowBackgrounds":0,"serializedVirtualizationData":{"scrollOffset":{"x":0.0,"y":0.0},"firstVisibleIndex":0,"contentPadding":0.0,"contentHeight":88.0,"anchoredItemIndex":0,"anchorOffset":0.0},"m_SelectedIds":[]}'
  - '{"m_ScrollOffset":{"x":0.0,"y":0.0}}'
  - '{"m_Value":0.0,"m_LowValue":0.0,"m_HighValue":0.0}'
  - '{"m_ShowAlternatingRowBackgrounds":0,"serializedVirtualizationData":{"scrollOffset":{"x":0.0,"y":0.0},"firstVisibleIndex":0,"contentPadding":0.0,"contentHeight":88.0,"anchoredItemIndex":0,"anchorOffset":0.0},"m_SelectedIds":[]}'
  - '{"m_ScrollOffset":{"x":0.0,"y":0.0}}'
  - '{"m_Value":0.0,"m_LowValue":0.0,"m_HighValue":0.0}'
  - '{"m_ShowAlternatingRowBackgrounds":0,"serializedVirtualizationData":{"scrollOffset":{"x":0.0,"y":0.0},"firstVisibleIndex":0,"contentPadding":0.0,"contentHeight":0.0,"anchoredItemIndex":0,"anchorOffset":0.0},"m_SelectedIds":[]}'
  - '{"m_ScrollOffset":{"x":0.0,"y":0.0}}'
  - '{"m_Value":0.0,"m_LowValue":0.0,"m_HighValue":0.0}'
  - '{"m_ShowAlternatingRowBackgrounds":0,"serializedVirtualizationData":{"scrollOffset":{"x":0.0,"y":0.0},"firstVisibleIndex":0,"contentPadding":0.0,"contentHeight":0.0,"anchoredItemIndex":0,"anchorOffset":0.0},"m_SelectedIds":[]}'
  - '{"m_ScrollOffset":{"x":0.0,"y":0.0}}'
  - '{"m_Value":0.0,"m_LowValue":0.0,"m_HighValue":0.0}'
  - '{}'
  - '{}'
  - '{}'
