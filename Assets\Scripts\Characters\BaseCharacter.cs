using System;
using System.Collections.Generic;
using UnityEngine;
using TacticalCombatSystem.Interfaces;
using TacticalCombatSystem.Interfaces.Core;
using TacticalCombatSystem.Battle;
using TacticalCombatSystem.Core;
using TacticalCombatSystem.Equipment;
using System.Linq;

namespace TacticalCombatSystem.Characters
{
    public class BaseCharacter : MonoBehaviour, ICombatParticipant
    {
        #region Events

        public event Action<int, int> OnHealthChanged;
        public event Action<int, int> OnManaChanged;
        public event Action<object> OnStatusEffectAdded;
        public event Action<object> OnStatusEffectRemoved;

        #endregion

        #region Properties

        public Character CharacterData { get; private set; }

        public string ParticipantName => CharacterData?.CharacterName ?? "Unknown";
        public string Name => CharacterData?.CharacterName ?? "Unknown";
        public int MaxHP => Mathf.RoundToInt(CharacterData?.GetModifiedStat(StatType.MaxHealth) ?? 0);
        public int MaxMP => Mathf.RoundToInt(CharacterData?.GetModifiedStat(StatType.MaxMana) ?? 0);
        public int Attack => Mathf.RoundToInt(CharacterData?.GetModifiedStat(StatType.PhysicalAttack) ?? 0);
        public int Defense => Mathf.RoundToInt(CharacterData?.GetModifiedStat(StatType.PhysicalDefense) ?? 0);
        public int MagicAttack => Mathf.RoundToInt(CharacterData?.GetModifiedStat(StatType.MagicAttack) ?? 0);
        public int MagicDefense => Mathf.RoundToInt(CharacterData?.GetModifiedStat(StatType.MagicDefense) ?? 0);
        public int Speed => Mathf.RoundToInt(CharacterData?.GetModifiedStat(StatType.Speed) ?? 0);
        public bool IsAlive => CurrentHP > 0;
        public bool IsPlayerControlled => gameObject.CompareTag("Player");
        public bool IsDefending => activeStatusEffects.Any(e => e.Name == "Defending");
        public bool IsPlayable => CharacterData?.IsPlayable ?? false;
        public Sprite Portrait => CharacterData != null ? CharacterData.Portrait : null;

        public int CurrentHP
        {
            get => currentHP;
            set
            {
                int oldHP = currentHP;
                currentHP = Mathf.Clamp(value, 0, MaxHP);
                if (oldHP != currentHP)
                {
                    OnHealthChanged?.Invoke(currentHP, MaxHP);
                }
            }
        }

        public int CurrentMP
        {
            get => currentMP;
            set
            {
                int oldMP = currentMP;
                currentMP = Mathf.Clamp(value, 0, MaxMP);
                if (oldMP != currentMP)
                {
                    OnManaChanged?.Invoke(currentMP, MaxMP);
                }
            }
        }

        public Transform GetTransform() => transform;

        #endregion

        #region Private Fields

        private IBattleManager battleManager;
        private int currentHP;
        private int currentMP;
        private Animator characterAnimator;
        private readonly List<IStatusEffect> activeStatusEffects = new List<IStatusEffect>();
        private CooldownManager cooldownManager;

        #endregion

        #region Serialized Fields

        [Header("Character Data")]
        [SerializeField] private Character characterData;

        #endregion

        #region Unity Methods

        private void Awake()
        {
            characterAnimator = GetComponentInChildren<Animator>();
            cooldownManager = new CooldownManager(this);

            if (characterData != null)
            {
                InitializeFromCharacterData(characterData);
            }
        }

        #endregion

        #region Initialization

        public void InitializeFromCharacterData(Character data)
        {
            this.CharacterData = data;
            this.characterData = data; // Keep serialized field in sync
            if (CharacterData != null)
            {
                CurrentHP = MaxHP;
                CurrentMP = MaxMP;
            }
        }

        public void InitializeForBattle(IBattleManager manager)
        {
            this.battleManager = manager;

            if (CharacterData == null)
            {
                Debug.LogError($"CharacterData is not assigned for {gameObject.name}. Battle initialization failed.");
                return;
            }

            // Initialize the character data if not already done
            CharacterData.Initialize();

            // Set up equipment manager reference
            if (CharacterData.Equipment != null)
            {
                // Update equipment manager to reference this BaseCharacter
                // This is a bit of a workaround since we can't easily modify the constructor
                var equipmentField = typeof(EquipmentManager).GetField("owner",
                    System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
                equipmentField?.SetValue(CharacterData.Equipment, this);
            }
        }

        #endregion

        #region ICombatParticipant Implementation

        public void OnTurnStart()
        {
            // Update cooldowns
            cooldownManager?.OnTurnStart();

            // Process status effects at turn start
            ProcessStatusEffectsAtTurnStart();
        }

        public void OnTurnEnd()
        {
            // Update cooldowns
            cooldownManager?.OnTurnEnd();

            // Process status effects at turn end
            ProcessStatusEffectsAtTurnEnd();
        }

        public void OnActionPerformed(ICombatAction action)
        {
            Debug.Log($"{ParticipantName} performed action: {action.ActionName}");

            // Start cooldown if the action has one
            if (action.Cooldown > 0)
            {
                cooldownManager?.StartCooldown(action);
            }
        }

        public void OnDamageTaken(int amount, ICombatParticipant source) { }

        public void OnHealed(int amount, ICombatParticipant source) { }

        public List<ICombatAction> GetAvailableActions()
        {
            var actions = new List<ICombatAction>();

            // Add the default actions for all characters.
            // We create instances because ScriptableObjects should be treated as templates.
            actions.Add(ScriptableObject.CreateInstance<TacticalCombatSystem.Actions.BasicAttackAction>());
            actions.Add(ScriptableObject.CreateInstance<TacticalCombatSystem.Actions.DefendAction>());
            actions.Add(ScriptableObject.CreateInstance<TacticalCombatSystem.Actions.RunAction>());

            // Add any special abilities from the character's data.
            if (CharacterData?.Abilities != null)
            {
                actions.AddRange(CharacterData.Abilities.Cast<ICombatAction>());
            }

            return actions;
        }

        public bool CanPerformAction(ICombatAction action)
        {
            if (!IsAlive) return false;
            if (CurrentMP < action.ManaCost) return false;
            if (CurrentHP <= action.ResourceCost && action.ResourceType == ResourceType.Health) return false;

            // Check cooldown
            if (cooldownManager != null && cooldownManager.IsOnCooldown(action)) return false;

            return true;
        }

        public List<ICombatParticipant> GetValidTargets(ICombatAction action)
        {
            if (battleManager == null) return new List<ICombatParticipant>();
            return battleManager.GetAllParticipants().Where(p => p.IsAlive && action.IsValidTarget(this, p)).ToList();
        }

        public void ApplyStatusEffect(IStatusEffect effect)
        {
            // Prevent duplicate status effects if needed (optional)
            if (activeStatusEffects.Any(se => se.Name == effect.Name)) return;

            activeStatusEffects.Add(effect);
            effect.OnApply(this);
            battleManager?.ReportStatusEffect(this, effect as StatusEffect);
            OnStatusEffectAdded?.Invoke(effect);
            Debug.Log($"{ParticipantName} is now affected by {effect.Name}");
        }

        public void RemoveStatusEffect(IStatusEffect effect)
        {
            var effectToRemove = activeStatusEffects.FirstOrDefault(se => se.Name == effect.Name);
            if (effectToRemove != null)
            {
                activeStatusEffects.Remove(effectToRemove);
                effectToRemove.OnRemove(this);
                OnStatusEffectRemoved?.Invoke(effectToRemove);
                Debug.Log($"{ParticipantName} is no longer affected by {effectToRemove.Name}");
            }
        }

        public bool HasStatusEffect(string effectName)
        {
            return activeStatusEffects.Any(e => e.Name == effectName);
        }

        public void TriggerAnimation(string triggerName)
        {
            if (characterAnimator != null && characterAnimator.runtimeAnimatorController != null)
            {
                characterAnimator.SetTrigger(triggerName);
            }
            else
            {
                Debug.LogWarning($"Animator not set or missing controller on {gameObject.name}");
            }
        }

        public void Highlight(Color color)
        {
            // Placeholder for visual highlighting logic (e.g., using the provided color)
            Debug.Log($"{ParticipantName} highlighted with color {color}.");
        }

        public void ResetHighlight()
        {
            // Placeholder for removing visual highlighting
            Debug.Log($"{ParticipantName} highlight removed.");
        }

        #endregion

        #region Public Combat Methods

        public void TakeDamage(int amount, ICombatParticipant source)
        {
            if (!IsAlive) return;

            int damageTaken = Mathf.Max(1, amount - Defense);
            CurrentHP -= damageTaken;

            Debug.Log($"{ParticipantName} took {damageTaken} damage!");
            battleManager?.ReportDamage(this, damageTaken);
            OnDamageTaken(damageTaken, source);

            // Show floating damage text
            ShowFloatingDamage(damageTaken, false);

            if (!IsAlive)
            {
                HandleDeath();
            }
        }

        /// <summary>
        /// Take damage using the enhanced damage system
        /// </summary>
        public void TakeDamage(TacticalCombatSystem.Core.DamageResult damageResult, ICombatParticipant source)
        {
            if (!IsAlive) return;

            CurrentHP -= damageResult.damage;

            string critText = damageResult.isCritical ? " (Critical!)" : "";
            string elementText = damageResult.element != TacticalCombatSystem.Actions.ElementType.None ? $" ({damageResult.element})" : "";
            Debug.Log($"{ParticipantName} took {damageResult.damage}{elementText} damage{critText}!");

            battleManager?.ReportDamage(this, damageResult.damage);
            OnDamageTaken(damageResult.damage, source);

            // Show floating damage text with enhanced info
            ShowFloatingDamage(damageResult);

            if (!IsAlive)
            {
                HandleDeath();
            }
        }

        public void Heal(int amount)
        {
            if (!IsAlive) return;
            int healAmount = Mathf.Min(amount, MaxHP - CurrentHP);
            CurrentHP += healAmount;
            battleManager?.ReportHeal(this, healAmount);
            OnHealed(healAmount, this);
            Debug.Log($"{ParticipantName} healed for {healAmount} HP!");

            // Show floating healing text
            ShowFloatingHealing(healAmount, false);
        }

        /// <summary>
        /// Heal using the enhanced healing system
        /// </summary>
        public void Heal(TacticalCombatSystem.Core.HealingResult healingResult)
        {
            if (!IsAlive) return;

            CurrentHP += healingResult.healing;
            CurrentHP = Mathf.Min(CurrentHP, MaxHP); // Ensure we don't exceed max HP

            string critText = healingResult.isCritical ? " (Critical!)" : "";
            Debug.Log($"{ParticipantName} healed for {healingResult.healing} HP{critText}!");

            battleManager?.ReportHeal(this, healingResult.healing);
            OnHealed(healingResult.healing, this);

            // Show floating healing text with enhanced info
            ShowFloatingHealing(healingResult);
        }

        #endregion

        #region Private Methods

        private void HandleDeath()
        {
            Debug.Log($"{ParticipantName} has been defeated!");
            characterAnimator?.SetTrigger("Die");
            battleManager?.RemoveFromBattle(this);
        }

        private void ProcessStatusEffectsAtTurnStart()
        {
            // Create a copy of the list to avoid modification during iteration
            var effectsCopy = new List<IStatusEffect>(activeStatusEffects);

            foreach (var effect in effectsCopy)
            {
                if (effect != null)
                {
                    effect.OnTurnStart(this);
                }
            }
        }

        private void ProcessStatusEffectsAtTurnEnd()
        {
            // Create a copy of the list to avoid modification during iteration
            var effectsCopy = new List<IStatusEffect>(activeStatusEffects);

            foreach (var effect in effectsCopy)
            {
                if (effect != null)
                {
                    effect.OnTurnEnd(this);
                }
            }
        }

        /// <summary>
        /// Get the cooldown manager for this character
        /// </summary>
        public CooldownManager GetCooldownManager()
        {
            return cooldownManager;
        }

        /// <summary>
        /// Show floating damage text
        /// </summary>
        private void ShowFloatingDamage(int damage, bool isCritical)
        {
            var floatingTextManager = TacticalCombatSystem.UI.FloatingTextManager.Instance;
            if (floatingTextManager != null)
            {
                var damageResult = new TacticalCombatSystem.Core.DamageResult
                {
                    damage = damage,
                    isCritical = isCritical,
                    element = TacticalCombatSystem.Actions.ElementType.None,
                    damageType = TacticalCombatSystem.Actions.DamageType.Physical
                };

                floatingTextManager.CreateDamageNumber(transform.position, damageResult);
            }
        }

        /// <summary>
        /// Show floating damage text with enhanced result
        /// </summary>
        private void ShowFloatingDamage(TacticalCombatSystem.Core.DamageResult damageResult)
        {
            var floatingTextManager = TacticalCombatSystem.UI.FloatingTextManager.Instance;
            if (floatingTextManager != null)
            {
                floatingTextManager.CreateDamageNumber(transform.position, damageResult);
            }
        }

        /// <summary>
        /// Show floating healing text
        /// </summary>
        private void ShowFloatingHealing(int healing, bool isCritical)
        {
            var floatingTextManager = TacticalCombatSystem.UI.FloatingTextManager.Instance;
            if (floatingTextManager != null)
            {
                var healingResult = new TacticalCombatSystem.Core.HealingResult
                {
                    healing = healing,
                    isCritical = isCritical
                };

                floatingTextManager.CreateHealingNumber(transform.position, healingResult);
            }
        }

        /// <summary>
        /// Show floating healing text with enhanced result
        /// </summary>
        private void ShowFloatingHealing(TacticalCombatSystem.Core.HealingResult healingResult)
        {
            var floatingTextManager = TacticalCombatSystem.UI.FloatingTextManager.Instance;
            if (floatingTextManager != null)
            {
                floatingTextManager.CreateHealingNumber(transform.position, healingResult);
            }
        }

        #endregion
    }
}
