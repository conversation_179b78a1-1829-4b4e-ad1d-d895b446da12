using System;
using System.Collections.Generic;
using UnityEngine;
using TacticalCombatSystem.Interfaces;

namespace TacticalCombatSystem.Core
{
    /// <summary>
    /// Manages cooldowns for actions and abilities
    /// </summary>
    [Serializable]
    public class CooldownManager
    {
        [SerializeField] private Dictionary<string, CooldownData> activeCooldowns = new Dictionary<string, CooldownData>();
        [SerializeField] private ICombatParticipant owner;
        
        // Events
        public event Action<string, int> OnCooldownStarted;
        public event Action<string, int> OnCooldownUpdated;
        public event Action<string> OnCooldownFinished;
        
        public CooldownManager(ICombatParticipant owner)
        {
            this.owner = owner;
            activeCooldowns = new Dictionary<string, CooldownData>();
        }
        
        /// <summary>
        /// Start a cooldown for an action
        /// </summary>
        public void StartCooldown(ICombatAction action)
        {
            if (action == null || action.Cooldown <= 0) return;
            
            string actionId = GetActionId(action);
            var cooldownData = new CooldownData
            {
                actionName = action.ActionName,
                remainingTurns = action.Cooldown,
                maxTurns = action.Cooldown,
                actionId = actionId
            };
            
            activeCooldowns[actionId] = cooldownData;
            OnCooldownStarted?.Invoke(actionId, action.Cooldown);
            
            Debug.Log($"{owner.ParticipantName}: {action.ActionName} on cooldown for {action.Cooldown} turns");
        }
        
        /// <summary>
        /// Start a cooldown by name and duration
        /// </summary>
        public void StartCooldown(string actionName, int duration)
        {
            if (string.IsNullOrEmpty(actionName) || duration <= 0) return;
            
            var cooldownData = new CooldownData
            {
                actionName = actionName,
                remainingTurns = duration,
                maxTurns = duration,
                actionId = actionName
            };
            
            activeCooldowns[actionName] = cooldownData;
            OnCooldownStarted?.Invoke(actionName, duration);
            
            Debug.Log($"{owner.ParticipantName}: {actionName} on cooldown for {duration} turns");
        }
        
        /// <summary>
        /// Reduce all cooldowns by the specified amount (usually called at turn end)
        /// </summary>
        public void ReduceCooldowns(int amount = 1)
        {
            var keysToRemove = new List<string>();
            
            foreach (var kvp in activeCooldowns)
            {
                var cooldown = kvp.Value;
                cooldown.remainingTurns -= amount;
                
                if (cooldown.remainingTurns <= 0)
                {
                    keysToRemove.Add(kvp.Key);
                    OnCooldownFinished?.Invoke(kvp.Key);
                    Debug.Log($"{owner.ParticipantName}: {cooldown.actionName} cooldown finished");
                }
                else
                {
                    OnCooldownUpdated?.Invoke(kvp.Key, cooldown.remainingTurns);
                }
            }
            
            // Remove finished cooldowns
            foreach (string key in keysToRemove)
            {
                activeCooldowns.Remove(key);
            }
        }
        
        /// <summary>
        /// Check if an action is on cooldown
        /// </summary>
        public bool IsOnCooldown(ICombatAction action)
        {
            if (action == null) return false;
            string actionId = GetActionId(action);
            return activeCooldowns.ContainsKey(actionId);
        }
        
        /// <summary>
        /// Check if an action is on cooldown by name
        /// </summary>
        public bool IsOnCooldown(string actionName)
        {
            return activeCooldowns.ContainsKey(actionName);
        }
        
        /// <summary>
        /// Get remaining cooldown turns for an action
        /// </summary>
        public int GetRemainingCooldown(ICombatAction action)
        {
            if (action == null) return 0;
            string actionId = GetActionId(action);
            return activeCooldowns.TryGetValue(actionId, out CooldownData data) ? data.remainingTurns : 0;
        }
        
        /// <summary>
        /// Get remaining cooldown turns by action name
        /// </summary>
        public int GetRemainingCooldown(string actionName)
        {
            return activeCooldowns.TryGetValue(actionName, out CooldownData data) ? data.remainingTurns : 0;
        }
        
        /// <summary>
        /// Reset a specific cooldown
        /// </summary>
        public void ResetCooldown(ICombatAction action)
        {
            if (action == null) return;
            string actionId = GetActionId(action);
            
            if (activeCooldowns.Remove(actionId))
            {
                OnCooldownFinished?.Invoke(actionId);
                Debug.Log($"{owner.ParticipantName}: {action.ActionName} cooldown reset");
            }
        }
        
        /// <summary>
        /// Reset a specific cooldown by name
        /// </summary>
        public void ResetCooldown(string actionName)
        {
            if (activeCooldowns.Remove(actionName))
            {
                OnCooldownFinished?.Invoke(actionName);
                Debug.Log($"{owner.ParticipantName}: {actionName} cooldown reset");
            }
        }
        
        /// <summary>
        /// Reset all cooldowns
        /// </summary>
        public void ResetAllCooldowns()
        {
            var actionNames = new List<string>(activeCooldowns.Keys);
            activeCooldowns.Clear();
            
            foreach (string actionName in actionNames)
            {
                OnCooldownFinished?.Invoke(actionName);
            }
            
            Debug.Log($"{owner.ParticipantName}: All cooldowns reset");
        }
        
        /// <summary>
        /// Modify a cooldown (positive to increase, negative to decrease)
        /// </summary>
        public void ModifyCooldown(ICombatAction action, int modification)
        {
            if (action == null) return;
            string actionId = GetActionId(action);
            
            if (activeCooldowns.TryGetValue(actionId, out CooldownData data))
            {
                data.remainingTurns += modification;
                
                if (data.remainingTurns <= 0)
                {
                    activeCooldowns.Remove(actionId);
                    OnCooldownFinished?.Invoke(actionId);
                    Debug.Log($"{owner.ParticipantName}: {action.ActionName} cooldown finished early");
                }
                else
                {
                    OnCooldownUpdated?.Invoke(actionId, data.remainingTurns);
                }
            }
        }
        
        /// <summary>
        /// Get all active cooldowns
        /// </summary>
        public Dictionary<string, CooldownData> GetActiveCooldowns()
        {
            return new Dictionary<string, CooldownData>(activeCooldowns);
        }
        
        /// <summary>
        /// Get cooldown progress (0.0 to 1.0, where 1.0 is ready)
        /// </summary>
        public float GetCooldownProgress(ICombatAction action)
        {
            if (action == null) return 1f;
            string actionId = GetActionId(action);
            
            if (activeCooldowns.TryGetValue(actionId, out CooldownData data))
            {
                return 1f - ((float)data.remainingTurns / data.maxTurns);
            }
            
            return 1f; // Not on cooldown = ready
        }
        
        /// <summary>
        /// Generate a unique ID for an action
        /// </summary>
        private string GetActionId(ICombatAction action)
        {
            // Use the action's name as ID. In a more complex system,
            // you might want to use the actual object instance or a GUID
            return action.ActionName;
        }
        
        /// <summary>
        /// Called when the owner's turn starts
        /// </summary>
        public void OnTurnStart()
        {
            // Some cooldowns might have special behavior on turn start
            // For now, just log active cooldowns
            if (activeCooldowns.Count > 0)
            {
                Debug.Log($"{owner.ParticipantName} has {activeCooldowns.Count} active cooldowns");
            }
        }
        
        /// <summary>
        /// Called when the owner's turn ends
        /// </summary>
        public void OnTurnEnd()
        {
            // Reduce cooldowns at the end of the turn
            ReduceCooldowns(1);
        }
    }
    
    /// <summary>
    /// Data structure for tracking individual cooldowns
    /// </summary>
    [Serializable]
    public class CooldownData
    {
        public string actionName;
        public string actionId;
        public int remainingTurns;
        public int maxTurns;
        public DateTime startTime; // For real-time cooldowns if needed
        
        public float Progress => 1f - ((float)remainingTurns / maxTurns);
        public bool IsFinished => remainingTurns <= 0;
    }
    
    /// <summary>
    /// Extension methods for easier cooldown management
    /// </summary>
    public static class CooldownExtensions
    {
        /// <summary>
        /// Check if an action can be used (not on cooldown and meets other requirements)
        /// </summary>
        public static bool CanUseAction(this ICombatParticipant participant, ICombatAction action, CooldownManager cooldownManager)
        {
            if (action == null || cooldownManager == null) return false;
            
            // Check basic requirements
            if (!action.CanBeUsedBy(participant)) return false;
            
            // Check cooldown
            if (cooldownManager.IsOnCooldown(action)) return false;
            
            return true;
        }
    }
}
