using System.Collections.Generic;
using UnityEngine;
using TacticalCombatSystem.Core;
using TacticalCombatSystem.Abilities;
using TacticalCombatSystem.Interfaces.Core;
using TacticalCombatSystem.Equipment;

namespace TacticalCombatSystem.Characters
{
    /// <summary>
    /// Character data container that defines a character's stats and abilities
    /// </summary>
    [CreateAssetMenu(fileName = "New Character", menuName = "Tactical Combat/Character")]
    public class Character : ScriptableObject, ICharacter
    {
        // ICharacter implementation
        public string CharacterName => characterName;
        public string Description => description;
        public Sprite Portrait => portrait;
        public GameObject CharacterPrefab => characterPrefab;
        public int MaxHealth => maxHealth;
        public int MaxMana => maxMana;
        public int Attack => attack;
        public int Defense => defense;
        public int MagicAttack => magicAttack;
        public int MagicDefense => magicDefense;
        public int Speed => speed;
        public int Luck => luck;
        public float CriticalChance => criticalChance;
        public float CriticalMultiplier => criticalMultiplier;
        public int Level => level;
        public int CurrentExp => currentExp;
        public int ExpToNextLevel => expToNextLevel;
        public string[] StartingAbilities => startingAbilities;
        public IReadOnlyList<Ability> Abilities => abilities.AsReadOnly();
        public IReadOnlyList<StatusEffect> StartingStatusEffects => startingStatusEffects.AsReadOnly();
        public GameObject BattleVisualPrefab => battleVisualPrefab;
        public RuntimeAnimatorController AnimatorController => animatorController;
        
        [Header("Basic Info")]
        [SerializeField] private string characterName = "New Character";
        [TextArea] [SerializeField] private string description = "Character description";
        [SerializeField] private Sprite portrait;
        [SerializeField] private GameObject characterPrefab;
        
        [Header("Base Stats")]
        [SerializeField] private int maxHealth = 100;
        [SerializeField] private int maxMana = 50;
        [SerializeField] private int attack = 10;
        [SerializeField] private int defense = 5;
        [SerializeField] private int magicAttack = 8;
        [SerializeField] private int magicDefense = 4;
        [SerializeField] private int speed = 10;
        [SerializeField] private int luck = 10;
        [SerializeField] private float criticalChance = 0.1f;
        [SerializeField] private float criticalMultiplier = 1.5f;

        [Header("Level and Experience")]
        [SerializeField] private int level = 1;
        [SerializeField] private int currentExp = 0;
        [SerializeField] private int expToNextLevel = 100;

        [Header("Abilities")]
        [SerializeField] private string[] startingAbilities = new string[0];
        
        [Header("Abilities")]
        [SerializeField] private List<Ability> abilities = new List<Ability>();

        [Header("Visuals")]
        [SerializeField] private GameObject battleVisualPrefab;
        [SerializeField] private RuntimeAnimatorController animatorController;

        [Header("Status Effects")]
        [SerializeField] private List<StatusEffect> startingStatusEffects = new List<StatusEffect>();

        [Header("Enhanced Stats System")]
        [SerializeField] private CharacterStats stats = new CharacterStats();

        [Header("Equipment")]
        [SerializeField] private List<EquipmentItem> startingEquipment = new List<EquipmentItem>();

        // Current stats (runtime only)
        [System.NonSerialized] public int currentHealth;
        [System.NonSerialized] public int currentMP;
        [System.NonSerialized] public bool isPlayerControlled;
        [System.NonSerialized] private EquipmentManager equipmentManager;
        
        /// <summary>
        /// Indicates whether this character can be controlled by the player
        /// </summary>
        [SerializeField] private bool isPlayable = false;
        
        /// <summary>
        /// Gets whether this character is playable by the player
        /// </summary>
        public bool IsPlayable => isPlayable;

        /// <summary>
        /// Gets the character's stats system
        /// </summary>
        public CharacterStats Stats => stats;

        /// <summary>
        /// Gets the character's equipment manager
        /// </summary>
        public EquipmentManager Equipment => equipmentManager;
        
        /// <summary>
        /// Initialize the character's runtime stats
        /// </summary>
        public void Initialize()
        {
            currentHealth = maxHealth;
            currentMP = maxMana;

            // Initialize enhanced stats system
            if (stats == null)
            {
                stats = new CharacterStats();
            }

            // Set base stats from legacy values
            stats.SetBaseStat(StatType.MaxHealth, maxHealth);
            stats.SetBaseStat(StatType.MaxMana, maxMana);
            stats.SetBaseStat(StatType.Strength, attack / 1.5f); // Reverse calculate primary stats
            stats.SetBaseStat(StatType.Intelligence, magicAttack / 1.5f);
            stats.SetBaseStat(StatType.Vitality, defense / 1.2f);
            stats.SetBaseStat(StatType.Agility, speed / 1.3f);
            stats.SetBaseStat(StatType.Dexterity, (criticalChance * 200f) - luck);
            stats.SetBaseStat(StatType.Luck, luck);
            stats.SetBaseStat(StatType.Level, level);

            // Initialize equipment manager
            equipmentManager = new EquipmentManager(null); // Will be set when BaseCharacter is created

            // Equip starting equipment
            foreach (var equipment in startingEquipment)
            {
                if (equipment != null)
                {
                    equipmentManager.EquipItem(equipment);
                }
            }
        }
        
        /// <summary>
        /// Check if the character is alive
        /// </summary>
        public bool IsAlive() => currentHealth > 0;

        /// <summary>
        /// Get resistance to a specific status effect
        /// </summary>
        public float GetStatusResistance(string statusEffectId)
        {
            // Use enhanced stats system for status resistance
            switch (statusEffectId.ToLower())
            {
                case "poison": return stats.GetModifiedStat(StatType.PoisonResistance);
                case "stun": return stats.GetModifiedStat(StatType.StunResistance);
                case "silence": return stats.GetModifiedStat(StatType.SilenceResistance);
                case "sleep": return stats.GetModifiedStat(StatType.SleepResistance);
                case "charm": return stats.GetModifiedStat(StatType.CharmResistance);
                case "fear": return stats.GetModifiedStat(StatType.FearResistance);
                default: return stats.GetModifiedStat(StatType.Luck) / 100f; // Base resistance from luck
            }
        }

        /// <summary>
        /// Get enhanced stat value (uses new stats system if available, falls back to legacy)
        /// </summary>
        public float GetModifiedStat(StatType statType)
        {
            if (stats != null)
            {
                return stats.GetModifiedStat(statType);
            }

            // Fallback to legacy stats
            switch (statType)
            {
                case StatType.MaxHealth: return maxHealth;
                case StatType.MaxMana: return maxMana;
                case StatType.PhysicalAttack: return attack;
                case StatType.MagicAttack: return magicAttack;
                case StatType.PhysicalDefense: return defense;
                case StatType.MagicDefense: return magicDefense;
                case StatType.Speed: return speed;
                case StatType.Luck: return luck;
                case StatType.CriticalChance: return criticalChance;
                case StatType.CriticalDamage: return criticalMultiplier;
                case StatType.Level: return level;
                default: return 0f;
            }
        }
        
        /// <summary>
        /// Apply damage to the character
        /// </summary>
        public void TakeDamage(int damage)
        {
            if (!IsAlive()) return;
            
            currentHealth = Mathf.Max(0, currentHealth - damage);
            
            if (currentHealth <= 0)
            {
                currentHealth = 0;
                // Handle death
            }
        }
        
        /// <summary>
        /// Creates a new instance of BaseCharacter with this character's data
        /// </summary>
        public BaseCharacter CreateInstance(Transform parent = null, bool isPlayerControlled = false)
        {
            if (characterPrefab == null)
            {
                Debug.LogError($"Character {characterName} has no prefab assigned!");
                return null;
            }
            
            // Instantiate the character prefab
            GameObject charObj = Instantiate(characterPrefab, parent);
            charObj.name = characterName;
            
            // Get or add BaseCharacter component
            BaseCharacter baseChar = charObj.GetComponent<BaseCharacter>();
            if (baseChar == null)
            {
                baseChar = charObj.AddComponent<BaseCharacter>();
            }
            
            // Set tag for control status
            charObj.tag = isPlayerControlled ? "Player" : "Enemy";

            // Initialize the character
            baseChar.InitializeFromCharacterData(this);
            
            return baseChar;
        }
        
        /// <summary>
        /// Creates a battle-ready character with visual representation
        /// </summary>
        public BattleCharacter CreateBattleInstance(Transform parent = null, bool isPlayerTeam = false)
        {
            if (battleVisualPrefab == null)
            {
                Debug.LogError($"Character {characterName} has no battle visual prefab assigned!");
                return null;
            }
            
            // Create the battle character
            GameObject battleObj = Instantiate(battleVisualPrefab, parent);
            battleObj.name = $"Battle_{characterName}";
            
            // Get or add BattleCharacter component
            BattleCharacter battleChar = battleObj.GetComponent<BattleCharacter>();
            if (battleChar == null)
            {
                battleChar = battleObj.AddComponent<BattleCharacter>();
            }
            
            // Initialize the battle character
            battleChar.Initialize(this, isPlayerTeam);
            
            return battleChar;
        }
    }
}
