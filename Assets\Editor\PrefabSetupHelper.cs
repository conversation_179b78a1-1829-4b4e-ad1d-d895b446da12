using System;
using System.IO;
using System.Collections.Generic;
using UnityEditor;
using UnityEngine;
using UnityEngine.UI;
using TMPro;
using TacticalCombatSystem.Core;
using TacticalCombatSystem.Characters;
using TacticalCombatSystem.UI;
using Object = UnityEngine.Object;
using TacticalCombatSystem; // For CharacterVisual


public class PrefabSetupHelper : EditorWindow
{
    private static PrefabSetupConfig config;
    private static readonly List<Object> cleanupList = new List<Object>();
    
    [InitializeOnLoadMethod]
    private static void Initialize()
    {
        LoadOrCreateConfig();
        EditorApplication.quitting += CleanupTemporaryObjects;
    }
    
    private static void LoadOrCreateConfig()
    {
        // Try to find existing config
        string[] guids = AssetDatabase.FindAssets("t:PrefabSetupConfig");
        
        if (guids.Length > 0)
        {
            string path = AssetDatabase.GUIDToAssetPath(guids[0]);
            config = AssetDatabase.LoadAssetAtPath<PrefabSetupConfig>(path);
        }
        
        // Create new config if none exists
        if (config == null)
        {
            config = CreateInstance<PrefabSetupConfig>();
            string configPath = "Assets/Editor/PrefabSetupConfig.asset";
            AssetDatabase.CreateAsset(config, configPath);
            AssetDatabase.SaveAssets();
            Debug.Log($"Created new PrefabSetupConfig at {configPath}");
        }
    }
    
    private static void CleanupTemporaryObjects()
    {
        foreach (var obj in cleanupList)
        {
            if (obj != null)
            {
                if (EditorUtility.IsPersistent(obj))
                {
                    Debug.LogWarning($"Cannot destroy persistent object: {obj.name}");
                    continue;
                }
                
                if (obj is GameObject go)
                {
                    Object.DestroyImmediate(go);
                }
                else if (obj is Component component)
                {
                    Object.DestroyImmediate(component.gameObject);
                }
                else
                {
                    Object.DestroyImmediate(obj);
                }
            }
        }
        cleanupList.Clear();
    }
    
    private static T CreateTemporaryObject<T>(string name) where T : Component
    {
        var go = new GameObject(name);
        cleanupList.Add(go);
        return go.AddComponent<T>();
    }
    
    private static void SafeDestroy(Object obj)
    {
        if (obj == null) return;
        
        if (EditorApplication.isPlaying)
        {
            Object.Destroy(obj);
        }
        else
        {
            Object.DestroyImmediate(obj);
        }
    }
    private static PrefabSetupConfig Config
    {
        get
        {
            if (config == null)
            {
                LoadOrCreateConfig();
            }
            return config;
        }
    }
    
    [MenuItem("Tactical Combat/Create Character Prefab")]
    public static void CreateCharacterPrefab()
    {
        try
        {
            EditorUtility.DisplayProgressBar("Creating Character Prefab", "Initializing...", 0f);
            
            // Ensure config is loaded
            if (Config == null)
            {
                Debug.LogError("Failed to load PrefabSetupConfig");
                return;
            }
            
            // Create required directories
            CreateDirectoryIfNeeded(Config.prefabPath);
            CreateDirectoryIfNeeded(Config.materialsPath);
            
            // Create a new character prefab
            EditorUtility.DisplayProgressBar("Creating Character Prefab", "Setting up prefab...", 0.2f);
            GameObject characterPrefab = CreateCharacterPrefabInstance();
            
            if (characterPrefab == null)
            {
                Debug.LogError("Failed to create character prefab: Could not instantiate prefab");
                return;
            }
            
            // Set up visual representation
            EditorUtility.DisplayProgressBar("Creating Character Prefab", "Setting up visuals...", 0.4f);
            if (!SetupCharacterVisuals(characterPrefab.transform))
            {
                Debug.LogError("Failed to set up character visuals");
                SafeDestroy(characterPrefab);
                return;
            }
            
            // Set up UI
            EditorUtility.DisplayProgressBar("Creating Character Prefab", "Setting up UI...", 0.6f);
            if (!SetupCharacterUI(characterPrefab.transform))
            {
                Debug.LogWarning("Character UI setup encountered issues");
            }
            
            // Save the prefab
            EditorUtility.DisplayProgressBar("Creating Character Prefab", "Saving prefab...", 0.8f);
            string prefabPath = SavePrefab(characterPrefab, Config.defaultPrefabName);
            
            if (!string.IsNullOrEmpty(prefabPath))
            {
                // Select the created prefab
                GameObject prefab = AssetDatabase.LoadAssetAtPath<GameObject>(prefabPath);
                Selection.activeObject = prefab;
                EditorGUIUtility.PingObject(prefab);
                Debug.Log($"Successfully created character prefab at: {prefabPath}");
            }
            
            // Cleanup
            SafeDestroy(characterPrefab);
        }
        catch (Exception e)
        {
            Debug.LogError($"Error creating character prefab: {e.Message}\n{e.StackTrace}");
        }
        finally
        {
            EditorUtility.ClearProgressBar();
            AssetDatabase.SaveAssets();
            AssetDatabase.Refresh();
        }
    }
    
    private static void CreateDirectoryIfNeeded(string path)
    {
        if (string.IsNullOrEmpty(path)) return;
        
        string fullPath = Path.Combine(Application.dataPath, path.Replace("Assets/", ""));
        if (!Directory.Exists(fullPath))
        {
            Directory.CreateDirectory(fullPath);
            AssetDatabase.Refresh();
        }
    }
    
    private static GameObject CreateCharacterPrefabInstance()
    {
        try
        {
            var characterPrefab = new GameObject(Config.defaultCharacterName);
            Undo.RegisterCreatedObjectUndo(characterPrefab, "Create Character Prefab");
            
            // Add required components
            var characterVisual = characterPrefab.AddComponent<CharacterVisual>();
            // characterPrefab.AddComponent<CharacterUI>(); // This is incorrect. CharacterUI is not a MonoBehaviour, but a controller for a UI Toolkit VisualElement.
            var boxCollider = characterPrefab.AddComponent<BoxCollider>();
            var rigidbody = characterPrefab.AddComponent<Rigidbody>();
            
            // Configure components
            boxCollider.size = Config.colliderSize;
            boxCollider.center = Config.colliderCenter;
            rigidbody.isKinematic = true;
            rigidbody.useGravity = false;
            
            return characterPrefab;
        }
        catch (Exception e)
        {
            Debug.LogError($"Failed to create character prefab instance: {e.Message}");
            return null;
        }
    }
    
    /// <summary>
    /// Sets up the character's visual representation
    /// </summary>
    private static bool SetupCharacterVisuals(Transform parent)
    {
        if (parent == null) return false;
        
        try
        {
            // Create a container for the character model
            var model = new GameObject("Model");
            Undo.RegisterCreatedObjectUndo(model, "Create Character Model");
            model.transform.SetParent(parent, false);
            
            // Add a simple capsule as a placeholder
            var capsule = GameObject.CreatePrimitive(PrimitiveType.Capsule);
            Undo.RegisterCreatedObjectUndo(capsule, "Create Capsule");
            capsule.transform.SetParent(model.transform, false);
            capsule.transform.localPosition = new Vector3(0, 1, 0);
            
            // Set up material
            var material = new Material(Shader.Find("Standard"))
            {
                color = Config.defaultCharacterColor
            };
            
            // Save the material
            string materialPath = Path.Combine(Config.materialsPath, $"{Config.defaultMaterialName}.mat");
            if (!AssetDatabase.LoadAssetAtPath<Material>(materialPath))
            {
                AssetDatabase.CreateAsset(material, materialPath);
                AssetDatabase.SaveAssets();
            }
            
            // Apply the material
            var renderer = capsule.GetComponent<Renderer>();
            if (renderer != null)
            {
                renderer.material = AssetDatabase.LoadAssetAtPath<Material>(materialPath);
            }
            
            // Add an animator
            var animator = model.AddComponent<Animator>();
            
            // Add billboard component for UI elements that should always face the camera
            model.AddComponent<TacticalCombatSystem.Characters.Billboard>();

            // Add the CharacterVisual component reference
            var characterVisual = parent.GetComponent<CharacterVisual>();
            if (characterVisual != null)
            {
                // Use SerializedObject to set the private visualRoot field
                var so = new SerializedObject(characterVisual);
                var visualRootProp = so.FindProperty("visualRoot");
                if (visualRootProp != null)
                {
                    visualRootProp.objectReferenceValue = model.transform;
                    so.ApplyModifiedProperties();
                }
                else
                {
                    Debug.LogWarning("Could not find 'visualRoot' property on CharacterVisual.");
                }
            }
            
            return true;
        }
        catch (Exception e)
        {
            Debug.LogError($"Failed to set up character visuals: {e.Message}\n{e.StackTrace}");
            return false;
        }
    }
    
    /// <summary>
    /// Sets up the character's UI elements.
    /// NOTE: This method is currently disabled as it assumes a UGUI-based UI, while the runtime uses UI Toolkit.
    /// The CharacterUI class is not a MonoBehaviour and cannot be attached to a prefab in this manner.
    /// </summary>
    private static bool SetupCharacterUI(Transform parent)
    {
        Debug.LogWarning("PrefabSetupHelper.SetupCharacterUI is disabled due to incompatibility with the UI Toolkit-based CharacterUI class.");
        return true;
    }
    
    /// <summary>
    /// Saves the character prefab to the specified path
    /// </summary>
    private static string SavePrefab(GameObject prefab, string baseName)
    {
        if (prefab == null || string.IsNullOrEmpty(baseName))
        {
            Debug.LogError("Invalid prefab or base name");
            return string.Empty;
        }
        
        try
        {
            // Ensure the directory exists
            CreateDirectoryIfNeeded(Config.prefabPath);
            
            // Generate a unique prefab path
            string path = Path.Combine(Config.prefabPath, $"{baseName}.prefab");
            int counter = 1;
            
            while (File.Exists(Path.Combine(Application.dataPath, path.Replace("Assets/", ""))))
            {
                path = Path.Combine(Config.prefabPath, $"{baseName}_{counter}.prefab");
                counter++;
                
                // Safety check to prevent infinite loops
                if (counter > 1000)
                {
                    Debug.LogError("Failed to find a unique prefab name after 1000 attempts");
                    return string.Empty;
                }
            }
            
            // Save the prefab
            PrefabUtility.SaveAsPrefabAsset(prefab, path);
            AssetDatabase.SaveAssets();
            AssetDatabase.Refresh();
            
            return path;
        }
        catch (Exception e)
        {
            Debug.LogError($"Failed to save prefab: {e.Message}\n{e.StackTrace}");
            return string.Empty;
        }
    }

    
    /// <summary>
    /// Validates the created prefab structure
    /// </summary>
    private static bool ValidatePrefab(GameObject prefab)
    {
        if (prefab == null) return false;
        
        try
        {
            bool isValid = true;
            
            // Check for required components
            foreach (var componentType in Config.requiredComponents)
            {
                var type = Type.GetType(componentType);
                if (type != null && prefab.GetComponent(type) == null)
                {
                    Debug.LogError($"Missing required component: {componentType}");
                    isValid = false;
                }
            }
            
            // Check for required child objects
            var requiredChildren = new[] { "Model", "CharacterCanvas" };
            foreach (var childName in requiredChildren)
            {
                if (prefab.transform.Find(childName) == null)
                {
                    Debug.LogError($"Missing required child object: {childName}");
                    isValid = false;
                }
            }
            
            return isValid;
        }
        catch (Exception e)
        {
            Debug.LogError($"Error validating prefab: {e.Message}\n{e.StackTrace}");
            return false;
        }
    }
    
    /// <summary>
    /// Shows the configuration window
    /// </summary>
    [MenuItem("Tactical Combat/Prefab Setup Config")]
    public static void ShowConfigWindow()
    {
        LoadOrCreateConfig();
        if (config != null)
        {
            Selection.activeObject = config;
            EditorGUIUtility.PingObject(config);
        }
        else
        {
            EditorUtility.DisplayDialog("Error", "Failed to load or create configuration", "OK");
        }
    }
    
    [MenuItem("Tactical Combat/Refresh Prefab Setup")]
    private static void RefreshSetup()
    {
        LoadOrCreateConfig();
        Debug.Log("Prefab Setup refreshed");
    }
}
