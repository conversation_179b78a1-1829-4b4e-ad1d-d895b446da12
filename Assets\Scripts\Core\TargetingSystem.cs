using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using TacticalCombatSystem.Interfaces;
using TacticalCombatSystem.Characters;

namespace TacticalCombatSystem.Core
{
    /// <summary>
    /// Advanced targeting system with line-of-sight, area effects, and visual feedback
    /// </summary>
    public class TargetingSystem : MonoBehaviour
    {
        [Header("Line of Sight")]
        public LayerMask obstacleLayerMask = 1; // What blocks line of sight
        public float raycastOffset = 0.5f; // Offset from ground for raycasting
        
        [Header("Visual Feedback")]
        public GameObject targetHighlightPrefab;
        public GameObject rangeIndicatorPrefab;
        public GameObject aoeIndicatorPrefab;
        public Material validTargetMaterial;
        public Material invalidTargetMaterial;
        public Material selectedTargetMaterial;
        
        [Header("Colors")]
        public Color validTargetColor = Color.green;
        public Color invalidTargetColor = Color.red;
        public Color selectedTargetColor = Color.yellow;
        public Color allyTargetColor = Color.blue;
        public Color enemyTargetColor = Color.red;
        public Color neutralTargetColor = Color.gray;
        
        // Active targeting state
        private ICombatParticipant currentUser;
        private ICombatAction currentAction;
        private List<ICombatParticipant> validTargets = new List<ICombatParticipant>();
        private List<ICombatParticipant> selectedTargets = new List<ICombatParticipant>();
        private List<GameObject> activeHighlights = new List<GameObject>();
        private GameObject rangeIndicator;
        private GameObject aoeIndicator;
        
        // Events
        public event Action<ICombatParticipant> OnTargetSelected;
        public event Action<ICombatParticipant> OnTargetDeselected;
        public event Action<List<ICombatParticipant>> OnTargetingComplete;
        public event Action OnTargetingCancelled;
        
        // Singleton pattern
        public static TargetingSystem Instance { get; private set; }
        
        private void Awake()
        {
            if (Instance != null && Instance != this)
            {
                Destroy(gameObject);
                return;
            }
            
            Instance = this;
            DontDestroyOnLoad(gameObject);
        }
        
        /// <summary>
        /// Start targeting mode for an action
        /// </summary>
        public void StartTargeting(ICombatParticipant user, ICombatAction action)
        {
            if (user == null || action == null) return;
            
            // Clear any existing targeting
            ClearTargeting();
            
            currentUser = user;
            currentAction = action;
            
            // Get all valid targets for this action
            validTargets = GetValidTargetsForAction(user, action);
            
            // Show visual feedback
            ShowTargetingVisuals();
            
            Debug.Log($"Started targeting for {action.ActionName}. {validTargets.Count} valid targets found.");
        }
        
        /// <summary>
        /// Select a target (for single target actions) or add to selection (for multi-target)
        /// </summary>
        public bool SelectTarget(ICombatParticipant target)
        {
            if (currentAction == null || target == null) return false;
            
            // Check if target is valid
            if (!validTargets.Contains(target)) return false;
            
            // Handle different targeting types
            switch (currentAction.TargetType)
            {
                case TargetType.SingleEnemy:
                case TargetType.SingleAlly:
                case TargetType.Self:
                    // Single target - replace selection
                    selectedTargets.Clear();
                    selectedTargets.Add(target);
                    OnTargetSelected?.Invoke(target);
                    CompleteTargeting();
                    return true;
                    
                case TargetType.AllEnemies:
                case TargetType.AllAllies:
                    // All targets of type - auto-select all valid
                    selectedTargets.Clear();
                    selectedTargets.AddRange(validTargets);
                    foreach (var t in selectedTargets)
                    {
                        OnTargetSelected?.Invoke(t);
                    }
                    CompleteTargeting();
                    return true;
                    
                default:
                    // Multi-target or custom - add to selection
                    if (!selectedTargets.Contains(target))
                    {
                        selectedTargets.Add(target);
                        OnTargetSelected?.Invoke(target);
                        UpdateTargetHighlights();
                    }
                    return true;
            }
        }
        
        /// <summary>
        /// Deselect a target
        /// </summary>
        public bool DeselectTarget(ICombatParticipant target)
        {
            if (selectedTargets.Remove(target))
            {
                OnTargetDeselected?.Invoke(target);
                UpdateTargetHighlights();
                return true;
            }
            return false;
        }
        
        /// <summary>
        /// Complete targeting and execute the action
        /// </summary>
        public void CompleteTargeting()
        {
            if (selectedTargets.Count > 0)
            {
                OnTargetingComplete?.Invoke(new List<ICombatParticipant>(selectedTargets));
            }
            
            ClearTargeting();
        }
        
        /// <summary>
        /// Cancel targeting
        /// </summary>
        public void CancelTargeting()
        {
            OnTargetingCancelled?.Invoke();
            ClearTargeting();
        }
        
        /// <summary>
        /// Clear all targeting state and visuals
        /// </summary>
        public void ClearTargeting()
        {
            currentUser = null;
            currentAction = null;
            validTargets.Clear();
            selectedTargets.Clear();
            
            ClearVisuals();
        }
        
        /// <summary>
        /// Check if a target is within line of sight
        /// </summary>
        public bool HasLineOfSight(ICombatParticipant from, ICombatParticipant to)
        {
            if (from == null || to == null) return false;
            
            Vector3 fromPos = from.GetTransform().position + Vector3.up * raycastOffset;
            Vector3 toPos = to.GetTransform().position + Vector3.up * raycastOffset;
            Vector3 direction = (toPos - fromPos).normalized;
            float distance = Vector3.Distance(fromPos, toPos);
            
            // Raycast to check for obstacles
            RaycastHit hit;
            if (Physics.Raycast(fromPos, direction, out hit, distance, obstacleLayerMask))
            {
                // Check if we hit the target or an obstacle
                var hitParticipant = hit.collider.GetComponent<BaseCharacter>();
                return hitParticipant != null && ReferenceEquals(hitParticipant, to);
            }
            
            return true; // No obstacles found
        }
        
        /// <summary>
        /// Check if a target is within range
        /// </summary>
        public bool IsInRange(ICombatParticipant from, ICombatParticipant to, int range)
        {
            if (from == null || to == null) return false;
            
            float distance = Vector3.Distance(
                from.GetTransform().position,
                to.GetTransform().position
            );
            
            return distance <= range;
        }
        
        /// <summary>
        /// Get all valid targets for an action
        /// </summary>
        private List<ICombatParticipant> GetValidTargetsForAction(ICombatParticipant user, ICombatAction action)
        {
            var targets = new List<ICombatParticipant>();
            
            // Get all participants from battle manager
            var battleManager = FindFirstObjectByType<TacticalCombatSystem.Battle.BattleManager>();
            if (battleManager == null) return targets;
            
            var allParticipants = battleManager.GetAllParticipants();
            
            foreach (var participant in allParticipants)
            {
                if (!participant.IsAlive) continue;
                
                // Check if target type matches
                if (!action.IsValidTarget(user, participant)) continue;
                
                // Check range
                if (!IsInRange(user, participant, action.range)) continue;
                
                // Check line of sight if required
                if (action.requiresLineOfSight && !HasLineOfSight(user, participant)) continue;
                
                targets.Add(participant);
            }
            
            return targets;
        }
        
        /// <summary>
        /// Show targeting visuals
        /// </summary>
        private void ShowTargetingVisuals()
        {
            // Show range indicator
            ShowRangeIndicator();
            
            // Highlight valid targets
            UpdateTargetHighlights();
        }
        
        /// <summary>
        /// Show range indicator around the user
        /// </summary>
        private void ShowRangeIndicator()
        {
            if (rangeIndicatorPrefab != null && currentUser != null && currentAction != null)
            {
                rangeIndicator = Instantiate(rangeIndicatorPrefab, currentUser.GetTransform().position, Quaternion.identity);
                
                // Scale the indicator based on action range
                float scale = currentAction.range * 2f; // Diameter = range * 2
                rangeIndicator.transform.localScale = Vector3.one * scale;
            }
        }
        
        /// <summary>
        /// Update target highlights
        /// </summary>
        private void UpdateTargetHighlights()
        {
            // Clear existing highlights
            foreach (var highlight in activeHighlights)
            {
                if (highlight != null) Destroy(highlight);
            }
            activeHighlights.Clear();
            
            // Create new highlights
            foreach (var target in validTargets)
            {
                CreateTargetHighlight(target);
            }
        }
        
        /// <summary>
        /// Create a highlight for a target
        /// </summary>
        private void CreateTargetHighlight(ICombatParticipant target)
        {
            if (targetHighlightPrefab == null || target == null) return;
            
            GameObject highlight = Instantiate(targetHighlightPrefab, target.GetTransform().position, Quaternion.identity);
            highlight.transform.SetParent(target.GetTransform());
            
            // Set color based on target relationship and selection state
            Color highlightColor = GetTargetColor(target);
            
            var renderer = highlight.GetComponent<Renderer>();
            if (renderer != null)
            {
                renderer.material.color = highlightColor;
            }
            
            activeHighlights.Add(highlight);
        }
        
        /// <summary>
        /// Get the appropriate color for a target
        /// </summary>
        private Color GetTargetColor(ICombatParticipant target)
        {
            if (selectedTargets.Contains(target))
            {
                return selectedTargetColor;
            }
            
            if (currentUser != null)
            {
                if (target == currentUser)
                {
                    return selectedTargetColor;
                }
                else if (target.IsPlayerControlled == currentUser.IsPlayerControlled)
                {
                    return allyTargetColor;
                }
                else
                {
                    return enemyTargetColor;
                }
            }
            
            return neutralTargetColor;
        }
        
        /// <summary>
        /// Clear all visual indicators
        /// </summary>
        private void ClearVisuals()
        {
            // Clear highlights
            foreach (var highlight in activeHighlights)
            {
                if (highlight != null) Destroy(highlight);
            }
            activeHighlights.Clear();
            
            // Clear range indicator
            if (rangeIndicator != null)
            {
                Destroy(rangeIndicator);
                rangeIndicator = null;
            }
            
            // Clear AoE indicator
            if (aoeIndicator != null)
            {
                Destroy(aoeIndicator);
                aoeIndicator = null;
            }
        }
        
        /// <summary>
        /// Get currently selected targets
        /// </summary>
        public List<ICombatParticipant> GetSelectedTargets()
        {
            return new List<ICombatParticipant>(selectedTargets);
        }
        
        /// <summary>
        /// Get valid targets for current action
        /// </summary>
        public List<ICombatParticipant> GetValidTargets()
        {
            return new List<ICombatParticipant>(validTargets);
        }
        
        /// <summary>
        /// Check if currently in targeting mode
        /// </summary>
        public bool IsTargeting => currentAction != null;
        
        /// <summary>
        /// Get current action being targeted
        /// </summary>
        public ICombatAction GetCurrentAction() => currentAction;
        
        /// <summary>
        /// Get current user
        /// </summary>
        public ICombatParticipant GetCurrentUser() => currentUser;
    }
}
