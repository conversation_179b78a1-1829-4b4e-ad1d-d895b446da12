using UnityEngine;
using System.Collections.Generic;
using TacticalCombatSystem.Interfaces;

namespace TacticalCombatSystem.Characters
{
    // This class is now redundant. The improved logic has been moved to BattleInitializer.
    // It is recommended to remove this file and update any scenes that use it.
    [System.Obsolete("CharacterSpawner logic has been merged into BattleInitializer.")]
    public class CharacterSpawner : MonoBehaviour 
    {
        // The contents of this script have been moved to BattleInitializer.cs and Billboard.cs
    }
}
