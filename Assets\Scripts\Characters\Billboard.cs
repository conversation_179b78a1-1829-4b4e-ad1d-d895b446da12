using UnityEngine;

namespace TacticalCombatSystem.Characters
{
    /// <summary>
    /// Simple billboard script to make a transform always face the camera.
    /// </summary>
    public class Billboard : MonoBehaviour
    {
        private UnityEngine.Camera _camera;

        private void Start()
        {
            _camera = UnityEngine.Camera.main;
        }

        private void LateUpdate()
        {
            if (_camera == null)
            {
                // Try to find camera again if it was null initially
                _camera = UnityEngine.Camera.main;
                if (_camera == null) return;
            }

            // More robust method to make the UI face the camera, handles camera rotation correctly.
            transform.LookAt(transform.position + _camera.transform.rotation * Vector3.forward,
                             _camera.transform.rotation * Vector3.up);
        }
    }
}