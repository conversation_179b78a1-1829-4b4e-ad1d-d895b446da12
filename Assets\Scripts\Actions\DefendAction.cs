using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using TacticalCombatSystem.Core;
using TacticalCombatSystem.Interfaces;
using TacticalCombatSystem.Characters;

namespace TacticalCombatSystem.Actions
{
    [CreateAssetMenu(menuName = "Tactical Combat/Actions/Defend")]
    public class DefendAction : CombatAction
    {
        [Header("Defend Settings")]
        public float damageReduction = 0.5f; // 50% damage reduction
        public float defenseBonus = 10f; // Additional defense points
        public bool restoresMana = false;
        public int manaRestoreAmount = 5;
        public bool restoresHealth = false;
        public int healthRestoreAmount = 3;

        public override string AnimationTriggerName => "Defend";
        public override TargetType TargetType => TargetType.Self;

        private void OnEnable()
        {
            actionName = "Defend";
            description = $"Reduces incoming damage by {damageReduction * 100}% until your next turn.";
            if (restoresMana) description += $" Restores {manaRestoreAmount} MP.";
            if (restoresHealth) description += $" Restores {healthRestoreAmount} HP.";

            validTargets = TargetTeam.Self;
            mpCost = 0;
            hpCost = 0;
        }

        public override void Execute(ICombatParticipant user, ICombatParticipant target)
        {
            // Create enhanced defending status effect
            var defendEffect = CreateDefendingEffect();
            user.ApplyStatusEffect(defendEffect);

            // Immediate benefits
            if (restoresMana && user.CurrentMP < user.MaxMP)
            {
                int manaToRestore = Mathf.Min(manaRestoreAmount, user.MaxMP - user.CurrentMP);
                user.CurrentMP += manaToRestore;
                Debug.Log($"{user.ParticipantName} restores {manaToRestore} MP!");
            }

            if (restoresHealth && user.CurrentHP < user.MaxHP)
            {
                int healthToRestore = Mathf.Min(healthRestoreAmount, user.MaxHP - user.CurrentHP);
                if (user is BaseCharacter baseChar)
                {
                    baseChar.Heal(healthToRestore);
                }
                else
                {
                    user.CurrentHP += healthToRestore;
                    user.OnHealed(healthToRestore, user);
                }
                Debug.Log($"{user.ParticipantName} restores {healthToRestore} HP!");
            }

            Debug.Log($"{user.ParticipantName} is defending!");
        }

        private StatusEffect CreateDefendingEffect()
        {
            var defendEffect = StatusEffect.CreateInstance<StatusEffect>();
            defendEffect.name = "Defending";
            defendEffect.statusName = "Defending";
            defendEffect.description = $"Incoming damage reduced by {damageReduction * 100}%. Defense increased by {defenseBonus}.";
            defendEffect.duration = 2; // Lasts until the end of the user's next turn
            defendEffect.statusType = StatusType.Buff;
            defendEffect.statusColor = new Color(0.2f, 0.2f, 0.8f, 1f); // Blue color
            defendEffect.showFloatingText = true;
            defendEffect.floatingText = "Defending";

            // Add defense bonus as stat modifier
            var defenseModifier = new StatModifier(
                StatType.PhysicalDefense,
                StatModifierType.Flat,
                defenseBonus,
                "Defending",
                3 // High priority for temporary combat effects
            );
            defenseModifier.isTemporary = true;
            defenseModifier.duration = 2f;

            defendEffect.statModifiers.Add(defenseModifier);

            // Also add magic defense bonus
            var magicDefenseModifier = new StatModifier(
                StatType.MagicDefense,
                StatModifierType.Flat,
                defenseBonus * 0.8f, // Slightly less magic defense bonus
                "Defending",
                3
            );
            magicDefenseModifier.isTemporary = true;
            magicDefenseModifier.duration = 2f;

            defendEffect.statModifiers.Add(magicDefenseModifier);

            return defendEffect;
        }
    }
}
