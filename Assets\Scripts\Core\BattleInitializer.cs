using UnityEngine;
using TacticalCombatSystem.Interfaces;
using TacticalCombatSystem.Interfaces.Core;
using System.Collections.Generic;
using TacticalCombatSystem.Battle;
using TacticalCombatSystem.Characters;

namespace TacticalCombatSystem.Core
{
    public class BattleInitializer : MonoBehaviour
    {
        [Header("Scene References")]
        public IBattleManager battleManager;
        public IBattleUI battleUI;
        public BattleInputHandler inputHandler;
        public UnityEngine.Camera battleCamera;
        
        [Header("Character Prefabs")]
        public GameObject[] playerPrefabs;
        public GameObject[] enemyPrefabs;
        
        [Header("Spawn Points")]
        public Transform[] playerSpawnPoints;
        public Transform[] enemySpawnPoints;
        
        [Header("Test Data")]
        public Character[] testPlayerCharacters;
        public Character[] testEnemyCharacters;
        
        private void Start()
        {
            // Validate that all necessary references are assigned.
            if (battleManager == null || battleUI == null || inputHandler == null || battleCamera == null)
            {
                Debug.LogError("BattleInitializer is missing one or more required references (BattleManager, BattleUI, InputHandler, or BattleCamera). Please use the 'Tactical Combat/Setup Battle Scene' tool or assign them manually.", this);
                return; // Stop execution to prevent further errors.
            }

            // Set up test teams if we have test characters
            if (testPlayerCharacters != null && testPlayerCharacters.Length > 0 &&
                testEnemyCharacters != null && testEnemyCharacters.Length > 0)
            {
                SetupTestBattle();
            }
            else
            {
                // Otherwise, spawn from prefabs
                SpawnCharacters();
            }

            // Start the battle
            battleManager.StartBattle();
        }
        
        private void SetupTestBattle()
        {   
            // Spawn player team from test data
            for (int i = 0; i < testPlayerCharacters.Length; i++)
            {
                if (testPlayerCharacters[i] != null && i < playerSpawnPoints.Length)
                {
                    var combatParticipant = SpawnCharacterFromData(testPlayerCharacters[i], playerSpawnPoints[i], true);
                    if (combatParticipant != null)
                    {
                        battleManager.AddToPlayerTeam(combatParticipant);
                    }
                }
            }

            // Spawn enemy team from test data
            for (int i = 0; i < testEnemyCharacters.Length; i++)
            {
                if (testEnemyCharacters[i] != null && i < enemySpawnPoints.Length)
                {
                    var combatParticipant = SpawnCharacterFromData(testEnemyCharacters[i], enemySpawnPoints[i], false);
                    if (combatParticipant != null)
                    {
                        battleManager.AddToEnemyTeam(combatParticipant);
                    }
                }
            }
            Debug.Log("Character spawning from Test Data completed");
        }
        
        private void SpawnCharacters()
        {
            // Spawn player characters
            for (int i = 0; i < playerPrefabs.Length && i < playerSpawnPoints.Length; i++)
            {
                if (playerPrefabs[i] != null && playerSpawnPoints[i] != null)
                {
                    var participant = SpawnCharacterFromPrefab(playerPrefabs[i], playerSpawnPoints[i]);
                    if (participant != null) battleManager.AddToPlayerTeam(participant);
                }
            }

            // Spawn enemy characters
            for (int i = 0; i < enemyPrefabs.Length && i < enemySpawnPoints.Length; i++)
            {
                if (enemyPrefabs[i] != null && enemySpawnPoints[i] != null)
                {
                    var participant = SpawnCharacterFromPrefab(enemyPrefabs[i], enemySpawnPoints[i]);
                    if (participant != null) battleManager.AddToEnemyTeam(participant);
                }
            }

            Debug.Log("Character spawning from Prefabs completed");
        }

        private ICombatParticipant SpawnCharacterFromData(Character characterData, Transform spawnPoint, bool isPlayerTeam)
        {
            if (characterData == null || spawnPoint == null) return null;

            // Use the Character's CreateInstance method to create a proper BaseCharacter
            var baseCharacter = characterData.CreateInstance(spawnPoint, isPlayerTeam);
            if (baseCharacter == null) return null;

            SetupSpawnedCharacter(baseCharacter.gameObject, characterData.CharacterName, spawnPoint);
            return baseCharacter;
        }

        private ICombatParticipant SpawnCharacterFromPrefab(GameObject prefab, Transform spawnPoint)
        {
            if (prefab == null || spawnPoint == null) return null;
            
            GameObject characterObj = Instantiate(prefab, spawnPoint.position, spawnPoint.rotation);
            var combatParticipant = characterObj.GetComponent<ICombatParticipant>();
            
            SetupSpawnedCharacter(characterObj, prefab.name, spawnPoint);
            return combatParticipant;
        }
        
        private void OnValidate()
        {
            // Ensure we have enough spawn points for the test characters
            if (testPlayerCharacters != null && testPlayerCharacters.Length > 0 && 
                (playerSpawnPoints == null || playerSpawnPoints.Length < testPlayerCharacters.Length))
            {
                Debug.LogWarning($"Not enough player spawn points for all test characters. Need {testPlayerCharacters.Length} but have {playerSpawnPoints?.Length ?? 0}.");
            }
            
            if (testEnemyCharacters != null && testEnemyCharacters.Length > 0 && 
                (enemySpawnPoints == null || enemySpawnPoints.Length < testEnemyCharacters.Length))
            {
                Debug.LogWarning($"Not enough enemy spawn points for all test characters. Need {testEnemyCharacters.Length} but have {enemySpawnPoints?.Length ?? 0}.");
            }
        }

        private void SetupSpawnedCharacter(GameObject characterObject, string characterName, Transform spawnPoint)
        {
            characterObject.transform.position = spawnPoint.position;
            characterObject.transform.rotation = spawnPoint.rotation;
            characterObject.name = $"Character_{characterName}";

            // Set up world space UI
            var canvas = characterObject.GetComponentInChildren<Canvas>();
            if (canvas != null)
            {
                canvas.worldCamera = battleCamera;
                var billboard = canvas.GetComponent<TacticalCombatSystem.Characters.Billboard>();
                if (billboard == null)
                {
                    billboard = canvas.gameObject.AddComponent<TacticalCombatSystem.Characters.Billboard>();
                }
            }
        }
    }
}
