%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &1
MonoBehaviour:
  m_ObjectHideFlags: 53
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 0355ba069dae4d24bf2e318d15442628, type: 3}
  m_Name: 
  m_EditorClassIdentifier: Unity.AI.Image::Unity.AI.Image.Services.SessionPersistence.TextureGeneratorSettings
  m_Session:
    settings:
      lastSelectedModels:
        serializedData:
        - key: 0
          value:
            rid: 1430556398715142345
        - key: 1
          value:
            rid: 1430556398715142346
        - key: 2
          value:
            rid: 1430556398715142347
        - key: 3
          value:
            rid: 1430556398715142348
        - key: 4
          value:
            rid: 1430556398715142349
        - key: 5
          value:
            rid: 1430556398715142350
      previewSettings:
        sizeFactor: 0.5
  references:
    version: 2
    RefIds:
    - rid: 1430556398715142345
      type: {class: ModelSelection, ns: Unity.AI.Image.Services.Stores.States, asm: Unity.AI.Image}
      data:
        modelID: c65c79a0-b2af-46da-a8e5-2fc106ec2ab6
    - rid: 1430556398715142346
      type: {class: ModelSelection, ns: Unity.AI.Image.Services.Stores.States, asm: Unity.AI.Image}
      data:
        modelID: 
    - rid: 1430556398715142347
      type: {class: ModelSelection, ns: Unity.AI.Image.Services.Stores.States, asm: Unity.AI.Image}
      data:
        modelID: 
    - rid: 1430556398715142348
      type: {class: ModelSelection, ns: Unity.AI.Image.Services.Stores.States, asm: Unity.AI.Image}
      data:
        modelID: 
    - rid: 1430556398715142349
      type: {class: ModelSelection, ns: Unity.AI.Image.Services.Stores.States, asm: Unity.AI.Image}
      data:
        modelID: 2d2d53fc-e209-4853-a358-738191781d9c
    - rid: 1430556398715142350
      type: {class: ModelSelection, ns: Unity.AI.Image.Services.Stores.States, asm: Unity.AI.Image}
      data:
        modelID: 
