using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using TMPro;
using TacticalCombatSystem.Interfaces;
using TacticalCombatSystem.Core;
using TacticalCombatSystem.Characters;

namespace TacticalCombatSystem.UI
{
    /// <summary>
    /// UI component for displaying turn order and initiative
    /// </summary>
    public class TurnOrderDisplay : MonoBehaviour
    {
        [Header("UI Elements")]
        public Transform turnOrderContainer;
        public GameObject turnOrderEntryPrefab;
        public TextMeshProUGUI roundNumberText;
        public TextMeshProUGUI currentTurnText;
        
        [Header("Colors")]
        public Color playerColor = Color.blue;
        public Color enemyColor = Color.red;
        public Color currentTurnColor = Color.yellow;
        public Color deadColor = Color.gray;
        
        [Header("Animation")]
        public float animationDuration = 0.3f;
        public AnimationCurve scaleCurve = AnimationCurve.EaseInOut(0f, 0.8f, 1f, 1f);
        
        private List<TurnOrderEntry> turnOrderEntries = new List<TurnOrderEntry>();
        private TurnOrderSystem turnOrderSystem;
        
        private void Start()
        {
            // Find turn order system
            var battleManager = FindFirstObjectByType<TacticalCombatSystem.Battle.BattleManager>();
            if (battleManager != null)
            {
                // Subscribe to turn order events
                // Note: This would need to be implemented in BattleManager
                // For now, we'll update manually
            }
        }
        
        /// <summary>
        /// Initialize the turn order display
        /// </summary>
        public void Initialize(TurnOrderSystem turnSystem)
        {
            turnOrderSystem = turnSystem;
            
            if (turnOrderSystem != null)
            {
                turnOrderSystem.OnTurnOrderChanged += OnTurnOrderChanged;
                turnOrderSystem.OnNewRound += OnNewRound;
                turnOrderSystem.OnTurnSkipped += OnTurnSkipped;
            }
            
            RefreshDisplay();
        }
        
        /// <summary>
        /// Refresh the entire display
        /// </summary>
        public void RefreshDisplay()
        {
            if (turnOrderSystem == null) return;
            
            ClearEntries();
            CreateEntries();
            UpdateCurrentTurn();
            UpdateRoundNumber();
        }
        
        /// <summary>
        /// Clear all turn order entries
        /// </summary>
        private void ClearEntries()
        {
            foreach (var entry in turnOrderEntries)
            {
                if (entry != null && entry.gameObject != null)
                {
                    Destroy(entry.gameObject);
                }
            }
            turnOrderEntries.Clear();
        }
        
        /// <summary>
        /// Create turn order entries
        /// </summary>
        private void CreateEntries()
        {
            if (turnOrderSystem == null || turnOrderEntryPrefab == null || turnOrderContainer == null) return;
            
            var turnOrderWithInitiative = turnOrderSystem.GetTurnOrderWithInitiative();
            
            for (int i = 0; i < turnOrderWithInitiative.Count; i++)
            {
                var (participant, initiative) = turnOrderWithInitiative[i];
                CreateTurnOrderEntry(participant, initiative, i);
            }
        }
        
        /// <summary>
        /// Create a single turn order entry
        /// </summary>
        private void CreateTurnOrderEntry(ICombatParticipant participant, float initiative, int index)
        {
            GameObject entryObj = Instantiate(turnOrderEntryPrefab, turnOrderContainer);
            TurnOrderEntry entry = entryObj.GetComponent<TurnOrderEntry>();
            
            if (entry == null)
            {
                entry = entryObj.AddComponent<TurnOrderEntry>();
            }
            
            entry.Initialize(participant, initiative, index);
            turnOrderEntries.Add(entry);
            
            // Set color based on participant type
            Color entryColor = GetParticipantColor(participant);
            entry.SetColor(entryColor);
        }
        
        /// <summary>
        /// Get color for a participant
        /// </summary>
        private Color GetParticipantColor(ICombatParticipant participant)
        {
            if (!participant.IsAlive)
            {
                return deadColor;
            }
            
            return participant.IsPlayerControlled ? playerColor : enemyColor;
        }
        
        /// <summary>
        /// Update current turn highlighting
        /// </summary>
        private void UpdateCurrentTurn()
        {
            if (turnOrderSystem == null) return;
            
            var currentParticipant = turnOrderSystem.GetCurrentParticipant();
            int currentIndex = turnOrderSystem.GetCurrentTurnIndex();
            
            // Update all entries
            for (int i = 0; i < turnOrderEntries.Count; i++)
            {
                var entry = turnOrderEntries[i];
                bool isCurrent = (i == currentIndex);
                
                entry.SetCurrent(isCurrent);
                
                if (isCurrent)
                {
                    entry.SetColor(currentTurnColor);
                    entry.AnimateHighlight();
                }
                else
                {
                    Color normalColor = GetParticipantColor(entry.Participant);
                    entry.SetColor(normalColor);
                }
            }
            
            // Update current turn text
            if (currentTurnText != null && currentParticipant != null)
            {
                currentTurnText.text = $"Current Turn: {currentParticipant.ParticipantName}";
            }
        }
        
        /// <summary>
        /// Update round number display
        /// </summary>
        private void UpdateRoundNumber()
        {
            if (roundNumberText != null && turnOrderSystem != null)
            {
                roundNumberText.text = $"Round {turnOrderSystem.GetCurrentRound()}";
            }
        }
        
        /// <summary>
        /// Handle turn order changed event
        /// </summary>
        private void OnTurnOrderChanged(ICombatParticipant participant, int turnIndex)
        {
            UpdateCurrentTurn();
        }
        
        /// <summary>
        /// Handle new round event
        /// </summary>
        private void OnNewRound(int roundNumber)
        {
            UpdateRoundNumber();
            
            // Animate round change
            if (roundNumberText != null)
            {
                StartCoroutine(AnimateRoundChange());
            }
        }
        
        /// <summary>
        /// Handle turn skipped event
        /// </summary>
        private void OnTurnSkipped(ICombatParticipant participant)
        {
            // Find the entry for this participant and animate skip
            var entry = turnOrderEntries.Find(e => e.Participant == participant);
            if (entry != null)
            {
                entry.AnimateSkip();
            }
        }
        
        /// <summary>
        /// Animate round number change
        /// </summary>
        private System.Collections.IEnumerator AnimateRoundChange()
        {
            Vector3 originalScale = roundNumberText.transform.localScale;
            
            // Scale up
            float timer = 0f;
            while (timer < animationDuration * 0.5f)
            {
                timer += Time.deltaTime;
                float progress = timer / (animationDuration * 0.5f);
                float scale = Mathf.Lerp(1f, 1.3f, scaleCurve.Evaluate(progress));
                roundNumberText.transform.localScale = originalScale * scale;
                yield return null;
            }
            
            // Scale down
            timer = 0f;
            while (timer < animationDuration * 0.5f)
            {
                timer += Time.deltaTime;
                float progress = timer / (animationDuration * 0.5f);
                float scale = Mathf.Lerp(1.3f, 1f, scaleCurve.Evaluate(progress));
                roundNumberText.transform.localScale = originalScale * scale;
                yield return null;
            }
            
            roundNumberText.transform.localScale = originalScale;
        }
        
        private void OnDestroy()
        {
            if (turnOrderSystem != null)
            {
                turnOrderSystem.OnTurnOrderChanged -= OnTurnOrderChanged;
                turnOrderSystem.OnNewRound -= OnNewRound;
                turnOrderSystem.OnTurnSkipped -= OnTurnSkipped;
            }
        }
    }
    
    /// <summary>
    /// Individual turn order entry UI component
    /// </summary>
    public class TurnOrderEntry : MonoBehaviour
    {
        [Header("UI Components")]
        public Image backgroundImage;
        public Image portraitImage;
        public TextMeshProUGUI nameText;
        public TextMeshProUGUI initiativeText;
        public GameObject currentTurnIndicator;
        
        [Header("Animation")]
        public float highlightDuration = 0.5f;
        public float skipAnimationDuration = 0.3f;
        
        public ICombatParticipant Participant { get; private set; }
        
        /// <summary>
        /// Initialize the entry
        /// </summary>
        public void Initialize(ICombatParticipant participant, float initiative, int index)
        {
            Participant = participant;
            
            // Set name
            if (nameText != null)
            {
                nameText.text = participant.ParticipantName;
            }
            
            // Set initiative
            if (initiativeText != null)
            {
                initiativeText.text = initiative.ToString("F1");
            }
            
            // Set portrait
            if (portraitImage != null && participant.Portrait != null)
            {
                portraitImage.sprite = participant.Portrait;
            }
            
            // Hide current turn indicator initially
            if (currentTurnIndicator != null)
            {
                currentTurnIndicator.SetActive(false);
            }
        }
        
        /// <summary>
        /// Set the color of the entry
        /// </summary>
        public void SetColor(Color color)
        {
            if (backgroundImage != null)
            {
                backgroundImage.color = color;
            }
        }
        
        /// <summary>
        /// Set whether this is the current turn
        /// </summary>
        public void SetCurrent(bool isCurrent)
        {
            if (currentTurnIndicator != null)
            {
                currentTurnIndicator.SetActive(isCurrent);
            }
        }
        
        /// <summary>
        /// Animate highlight for current turn
        /// </summary>
        public void AnimateHighlight()
        {
            StartCoroutine(HighlightAnimation());
        }
        
        /// <summary>
        /// Animate skip effect
        /// </summary>
        public void AnimateSkip()
        {
            StartCoroutine(SkipAnimation());
        }
        
        /// <summary>
        /// Highlight animation coroutine
        /// </summary>
        private System.Collections.IEnumerator HighlightAnimation()
        {
            Vector3 originalScale = transform.localScale;
            
            float timer = 0f;
            while (timer < highlightDuration)
            {
                timer += Time.deltaTime;
                float progress = timer / highlightDuration;
                float scale = 1f + Mathf.Sin(progress * Mathf.PI * 2f) * 0.1f;
                transform.localScale = originalScale * scale;
                yield return null;
            }
            
            transform.localScale = originalScale;
        }
        
        /// <summary>
        /// Skip animation coroutine
        /// </summary>
        private System.Collections.IEnumerator SkipAnimation()
        {
            Color originalColor = backgroundImage.color;
            Vector3 originalScale = transform.localScale;
            
            // Fade out and scale down
            float timer = 0f;
            while (timer < skipAnimationDuration)
            {
                timer += Time.deltaTime;
                float progress = timer / skipAnimationDuration;
                
                Color color = originalColor;
                color.a = Mathf.Lerp(1f, 0.3f, progress);
                backgroundImage.color = color;
                
                float scale = Mathf.Lerp(1f, 0.8f, progress);
                transform.localScale = originalScale * scale;
                
                yield return null;
            }
            
            // Fade back in and scale up
            timer = 0f;
            while (timer < skipAnimationDuration)
            {
                timer += Time.deltaTime;
                float progress = timer / skipAnimationDuration;
                
                Color color = originalColor;
                color.a = Mathf.Lerp(0.3f, 1f, progress);
                backgroundImage.color = color;
                
                float scale = Mathf.Lerp(0.8f, 1f, progress);
                transform.localScale = originalScale * scale;
                
                yield return null;
            }
            
            backgroundImage.color = originalColor;
            transform.localScale = originalScale;
        }
    }
}
