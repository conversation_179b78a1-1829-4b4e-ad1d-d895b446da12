using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using TacticalCombatSystem.Core;
using TacticalCombatSystem.Interfaces;
using TacticalCombatSystem.Characters;
using TacticalCombatSystem.Equipment;

namespace TacticalCombatSystem.Actions
{
    [CreateAssetMenu(menuName = "Tactical Combat/Actions/Basic Attack")]
    public class BasicAttackAction : CombatAction
    {
        [Header("Basic Attack Settings")]
        public int baseDamage = 10;
        public DamageType damageType = DamageType.Physical;
        public bool canCrit = true;
        [Range(0f, 1f)] public float baseCritChance = 0.05f;
        public float baseCritMultiplier = 1.5f;

        [Head<PERSON>("Accuracy and Variance")]
        [Range(0f, 1f)] public float baseAccuracy = 0.95f;
        [Range(0f, 0.5f)] public float damageVariance = 0.1f; // ±10% damage variance

        [Header("Elemental Properties")]
        public ElementType elementType = ElementType.None;

        [<PERSON><PERSON>("Animation")]
        [Tooltip("The name of the animation trigger to play when this action is executed.")]
        [SerializeField] private string animationTrigger = "Attack";
        public override string AnimationTriggerName => animationTrigger;
        
        public override TargetType TargetType => TargetType.SingleEnemy;
        
        private void OnEnable()
        {
            // Set up default values
            actionName = "Basic Attack";
            description = "A basic attack that deals physical damage.";
            validTargets = TargetTeam.Enemy;
            range = 1;
            mpCost = 0;
        }
        
        public override void Execute(ICombatParticipant user, ICombatParticipant target)
        {
            Debug.Log($"{user.ParticipantName} attacks {target.ParticipantName} with {actionName}!");

            // Check accuracy first
            if (!CheckAccuracy(user, target))
            {
                Debug.Log($"{user.ParticipantName}'s attack missed {target.ParticipantName}!");
                ApplyCooldown();
                return;
            }

            // Pay costs
            user.CurrentMP -= mpCost;
            user.CurrentHP = Mathf.Max(1, user.CurrentHP - hpCost);

            // Calculate damage using enhanced system
            var damageResult = CalculateEnhancedDamage(user, target);

            // Apply final damage
            if (damageResult.damage > 0)
            {
                // Apply damage to target (using the character's TakeDamage method for proper handling)
                if (target is BaseCharacter baseTarget)
                {
                    baseTarget.TakeDamage(damageResult.damage, user);
                }
                else
                {
                    target.CurrentHP -= damageResult.damage;
                    target.OnDamageTaken(damageResult.damage, user);
                }

                // Log results
                string critText = damageResult.isCritical ? " (Critical Hit!)" : "";
                string elementText = elementType != ElementType.None ? $" ({elementType})" : "";
                Debug.Log($"{user.ParticipantName} deals {damageResult.damage}{elementText} damage to {target.ParticipantName}{critText}");
            }

            // Apply any status effects
            foreach (var effect in statusEffects)
            {
                if (effect != null)
                {
                    target.ApplyStatusEffect(Instantiate(effect));
                }
            }

            // Apply cooldown
            ApplyCooldown();
        }

        /// <summary>
        /// Check if the attack hits the target
        /// </summary>
        private bool CheckAccuracy(ICombatParticipant user, ICombatParticipant target)
        {
            float accuracy = baseAccuracy;
            float evasion = 0f;

            // Get enhanced stats if available
            if (user is BaseCharacter userChar && userChar.CharacterData != null)
            {
                accuracy = userChar.CharacterData.GetModifiedStat(StatType.Accuracy);
            }

            if (target is BaseCharacter targetChar && targetChar.CharacterData != null)
            {
                evasion = targetChar.CharacterData.GetModifiedStat(StatType.Evasion);
            }

            float hitChance = Mathf.Clamp01(accuracy - evasion);
            return Random.value <= hitChance;
        }

        /// <summary>
        /// Calculate damage using the enhanced stats system
        /// </summary>
        private DamageResult CalculateEnhancedDamage(ICombatParticipant user, ICombatParticipant target)
        {
            var result = new DamageResult();

            // Base damage with variance
            float damage = baseDamage;
            if (damageVariance > 0f)
            {
                float variance = Random.Range(-damageVariance, damageVariance);
                damage *= (1f + variance);
            }

            // Add weapon damage if equipped
            damage += GetWeaponDamage(user);

            // Apply user's attack stat
            float attackStat = GetAttackStat(user);
            damage += attackStat;

            // Apply target's defense
            float defenseStat = GetDefenseStat(target);
            damage *= CalculateDefenseReduction(defenseStat);

            // Check for critical hit
            result.isCritical = CheckCriticalHit(user);
            if (result.isCritical)
            {
                float critMultiplier = GetCriticalMultiplier(user);
                damage *= critMultiplier;
            }

            // Apply elemental modifiers
            damage *= GetElementalModifier(target);

            // Apply defend bonus
            if (target.IsDefending)
            {
                damage *= 0.5f; // 50% damage reduction
                Debug.Log($"{target.ParticipantName} is defending! Damage reduced.");
            }

            result.damage = Mathf.Max(1, Mathf.RoundToInt(damage)); // Minimum 1 damage
            return result;
        }

        private float GetWeaponDamage(ICombatParticipant user)
        {
            if (user is BaseCharacter baseChar && baseChar.CharacterData?.Equipment != null)
            {
                var weapon = baseChar.CharacterData.Equipment.GetEquippedItem(EquipmentSlot.Weapon);
                if (weapon != null)
                {
                    return weapon.CalculateDamage();
                }
            }
            return 0f;
        }

        private float GetAttackStat(ICombatParticipant user)
        {
            if (user is BaseCharacter baseChar && baseChar.CharacterData != null)
            {
                return damageType switch
                {
                    DamageType.Physical => baseChar.CharacterData.GetModifiedStat(StatType.PhysicalAttack),
                    DamageType.Magical => baseChar.CharacterData.GetModifiedStat(StatType.MagicAttack),
                    DamageType.True => 0f,
                    _ => user.Attack
                };
            }

            return damageType switch
            {
                DamageType.Physical => user.Attack,
                DamageType.Magical => user.MagicAttack,
                DamageType.True => 0f,
                _ => user.Attack
            };
        }

        private float GetDefenseStat(ICombatParticipant target)
        {
            if (target is BaseCharacter baseChar && baseChar.CharacterData != null)
            {
                return damageType switch
                {
                    DamageType.Physical => baseChar.CharacterData.GetModifiedStat(StatType.PhysicalDefense),
                    DamageType.Magical => baseChar.CharacterData.GetModifiedStat(StatType.MagicDefense),
                    DamageType.True => 0f,
                    _ => target.Defense
                };
            }

            return damageType switch
            {
                DamageType.Physical => target.Defense,
                DamageType.Magical => target.MagicDefense,
                DamageType.True => 0f,
                _ => target.Defense
            };
        }

        private float CalculateDefenseReduction(float defense)
        {
            if (damageType == DamageType.True) return 1f;

            // Defense formula: damage reduction = defense / (defense + 100)
            return 100f / (100f + defense);
        }

        private bool CheckCriticalHit(ICombatParticipant user)
        {
            if (!canCrit) return false;

            float critChance = baseCritChance;

            if (user is BaseCharacter baseChar && baseChar.CharacterData != null)
            {
                critChance = baseChar.CharacterData.GetModifiedStat(StatType.CriticalChance);

                // Add weapon crit bonus
                var weapon = baseChar.CharacterData.Equipment?.GetEquippedItem(EquipmentSlot.Weapon);
                if (weapon != null)
                {
                    critChance += weapon.CriticalChanceBonus;
                }
            }

            return Random.value <= critChance;
        }

        private float GetCriticalMultiplier(ICombatParticipant user)
        {
            if (user is BaseCharacter baseChar && baseChar.CharacterData != null)
            {
                return baseChar.CharacterData.GetModifiedStat(StatType.CriticalDamage);
            }

            return baseCritMultiplier;
        }

        private float GetElementalModifier(ICombatParticipant target)
        {
            if (elementType == ElementType.None) return 1f;

            if (target is BaseCharacter baseChar && baseChar.CharacterData != null)
            {
                StatType resistanceStat = elementType switch
                {
                    ElementType.Fire => StatType.FireResistance,
                    ElementType.Ice => StatType.IceResistance,
                    ElementType.Lightning => StatType.LightningResistance,
                    ElementType.Earth => StatType.EarthResistance,
                    ElementType.Wind => StatType.WindResistance,
                    ElementType.Water => StatType.WaterResistance,
                    ElementType.Light => StatType.LightResistance,
                    ElementType.Dark => StatType.DarkResistance,
                    _ => StatType.PhysicalDefense
                };

                float resistance = baseChar.CharacterData.GetModifiedStat(resistanceStat);
                return Mathf.Clamp(1f - resistance, 0.1f, 2f); // 10% minimum damage, 200% maximum
            }

            return 1f;
        }
        
        public override void OnSelected(ICombatParticipant user)
        {
            base.OnSelected(user);
            Debug.Log($"{actionName} selected by {user.ParticipantName}");
        }
        
        public override void OnDeselected(ICombatParticipant user)
        {
            base.OnDeselected(user);
            Debug.Log($"{actionName} deselected by {user.ParticipantName}");
        }
    }
    
    public enum DamageType
    {
        Physical,
        Magical,
        True
    }

    public enum ElementType
    {
        None,
        Fire,
        Ice,
        Lightning,
        Earth,
        Wind,
        Water,
        Light,
        Dark
    }

    public struct DamageResult
    {
        public int damage;
        public bool isCritical;
        public ElementType element;
        public DamageType damageType;
    }
}
