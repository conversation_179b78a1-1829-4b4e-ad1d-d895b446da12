using System.Collections.Generic;
using UnityEngine;
using TacticalCombatSystem.Interfaces.Core;
using TacticalCombatSystem.Core;

namespace TacticalCombatSystem.Interfaces
{
    /// <summary>
    /// Target team for combat actions
    /// </summary>
    public enum TargetTeam
    {
        Self,
        Ally,
        Allies,
        Enemy,
        All
    }

    /// <summary>
    /// Interface defining the contract for combat actions in the game
    /// </summary>
    public interface ICombatAction
    {
        // Basic info
        string ActionName { get; }
        string Description { get; }
        Sprite Icon { get; }
        
        // Targeting
        bool CanTargetSelf { get; }
        bool CanTargetAllies { get; }
        bool CanTargetEnemies { get; }
        TargetTeam ValidTargets { get; }
        int range { get; }
        
        // Execution
        bool CanBeUsedBy(TacticalCombatSystem.Interfaces.ICombatParticipant user);
        bool IsValidTarget(TacticalCombatSystem.Interfaces.ICombatParticipant user, TacticalCombatSystem.Interfaces.ICombatParticipant target);
        void Execute(TacticalCombatSystem.Interfaces.ICombatParticipant user, TacticalCombatSystem.Interfaces.ICombatParticipant target);
        System.Collections.IEnumerator ExecuteCoroutine(TacticalCombatSystem.Interfaces.ICombatParticipant user, TacticalCombatSystem.Interfaces.ICombatParticipant target, System.Action onComplete);
        
        // Cooldown and resource costs
        int ManaCost { get; }
        int MPCost => ManaCost; // Alias for ManaCost for compatibility
        int Cooldown { get; }
        int CurrentCooldown { get; set; }
        
        // Targeting behavior
        TargetType TargetType { get; }
        
        // Visual feedback
        string AnimationTriggerName { get; }
        void PlayVFX(Vector3 position);
        
        // Menu System
        /// <summary>
        /// The category this action belongs to (Basic, Physical, Magical, etc.)
        /// </summary>
        ActionCategory Category { get; }
        
        /// <summary>
        /// The type of resource this action consumes (None, Energy, Ether, Item)
        /// </summary>
        ResourceType ResourceType { get; }
        
        /// <summary>
        /// The amount of resources this action consumes
        /// </summary>
        int ResourceCost { get; }
    }
}
