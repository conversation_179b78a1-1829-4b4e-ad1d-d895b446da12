using UnityEngine;
using TMPro;
using TacticalCombatSystem.Core;
using TacticalCombatSystem.Actions;

namespace TacticalCombatSystem.UI
{
    public class DamageNumber : MonoBehaviour
    {
        [SerializeField] private TextMeshProUGUI damageText;
        [SerializeField] private float lifetime = 1f;
        [SerializeField] private float floatSpeed = 2f;
        [SerializeField] private AnimationCurve sizeCurve = AnimationCurve.EaseInOut(0, 0.5f, 1, 1.5f);
        [SerializeField] private AnimationCurve alphaCurve = AnimationCurve.EaseInOut(0, 1, 1, 0);

        [Header("Colors")]
        [SerializeField] private Color damageColor = Color.red;
        [SerializeField] private Color criticalDamageColor = Color.yellow;
        [SerializeField] private Color healingColor = Color.green;
        [SerializeField] private Color criticalHealingColor = Color.cyan;
        [SerializeField] private Color missColor = Color.gray;
        [SerializeField] private Color statusEffectColor = Color.purple;
        
        private float timer;
        private Vector3 startPosition;
        private Color startColor;
        private System.Action<DamageNumber> _returnToPool;
        
        private void Awake()
        {
            if (damageText == null)
                damageText = GetComponentInChildren<TextMeshProUGUI>();
                
            startColor = damageText.color;
        }

        public void Initialize(System.Action<DamageNumber> returnAction)
        {
            _returnToPool = returnAction;
            gameObject.SetActive(false);
        }
        
        public void ShowNumber(int amount, bool isDamage)
        {
            // Legacy method for backward compatibility
            if (isDamage)
            {
                ShowDamage(amount, false);
            }
            else
            {
                ShowHealing(amount, false);
            }
        }

        /// <summary>
        /// Show damage with enhanced formatting
        /// </summary>
        public void ShowDamage(int amount, bool isCritical, ElementType element = ElementType.None)
        {
            string text = amount.ToString();
            Color color = isCritical ? criticalDamageColor : damageColor;

            // Add special formatting for critical hits
            if (isCritical)
            {
                text = $"<b>{text}!</b>";
                transform.localScale = Vector3.one * 1.2f; // Bigger for crits
            }

            // Add elemental indicator
            if (element != ElementType.None)
            {
                text += $"\n<size=60%>({element})</size>";
            }

            SetupAndAnimate(text, color);
        }

        /// <summary>
        /// Show damage using DamageResult
        /// </summary>
        public void ShowDamage(TacticalCombatSystem.Core.DamageResult damageResult)
        {
            ShowDamage(damageResult.damage, damageResult.isCritical, damageResult.element);
        }

        /// <summary>
        /// Show healing with enhanced formatting
        /// </summary>
        public void ShowHealing(int amount, bool isCritical)
        {
            string text = $"+{amount}";
            Color color = isCritical ? criticalHealingColor : healingColor;

            // Add special formatting for critical heals
            if (isCritical)
            {
                text = $"<b>{text}!</b>";
                transform.localScale = Vector3.one * 1.2f; // Bigger for crits
            }

            SetupAndAnimate(text, color);
        }

        /// <summary>
        /// Show healing using HealingResult
        /// </summary>
        public void ShowHealing(TacticalCombatSystem.Core.HealingResult healingResult)
        {
            ShowHealing(healingResult.healing, healingResult.isCritical);
        }

        /// <summary>
        /// Show miss text
        /// </summary>
        public void ShowMiss()
        {
            SetupAndAnimate("MISS", missColor);
        }

        /// <summary>
        /// Show status effect text
        /// </summary>
        public void ShowStatusEffect(string effectName, bool isPositive = false)
        {
            Color color = isPositive ? healingColor : statusEffectColor;
            SetupAndAnimate(effectName, color);
        }

        /// <summary>
        /// Setup text and start animation
        /// </summary>
        private void SetupAndAnimate(string text, Color color)
        {
            // Reset state
            timer = 0f;
            startPosition = transform.position;

            // Set text and color
            damageText.text = text;
            damageText.color = color;

            // Make sure the object is active
            gameObject.SetActive(true);

            // Start the animation
            StartCoroutine(Animate());
        }
        
        private System.Collections.IEnumerator Animate()
        {
            while (timer < lifetime)
            {
                timer += Time.deltaTime;
                float t = timer / lifetime;
                
                // Update position
                transform.position = startPosition + Vector3.up * (floatSpeed * t);
                
                // Update scale
                float scale = sizeCurve.Evaluate(t);
                transform.localScale = Vector3.one * scale;
                
                // Update alpha
                Color color = damageText.color;
                color.a = alphaCurve.Evaluate(t);
                damageText.color = color;
                
                yield return null;
            }
            
            // Return to pool
            _returnToPool?.Invoke(this);
        }
        
        private void OnDisable()
        {
            // Reset any state
            if (damageText != null)
            {
                damageText.color = startColor;
                damageText.text = string.Empty;
            }
            transform.localScale = Vector3.one;
        }
    }

    /// <summary>
    /// Manager for creating and managing floating combat text
    /// </summary>
    public class FloatingTextManager : MonoBehaviour
    {
        [Header("Prefabs")]
        public GameObject damageNumberPrefab;

        [Header("Positioning")]
        public Vector3 spawnOffset = Vector3.up;
        public float randomSpread = 0.5f;

        public static FloatingTextManager Instance { get; private set; }

        private void Awake()
        {
            if (Instance != null && Instance != this)
            {
                Destroy(gameObject);
                return;
            }

            Instance = this;
        }

        /// <summary>
        /// Create damage number at position
        /// </summary>
        public void CreateDamageNumber(Vector3 position, TacticalCombatSystem.Core.DamageResult damageResult)
        {
            if (damageNumberPrefab == null) return;

            Vector3 spawnPos = GetRandomizedPosition(position);
            GameObject numberObj = Instantiate(damageNumberPrefab, spawnPos, Quaternion.identity);

            DamageNumber damageNumber = numberObj.GetComponent<DamageNumber>();
            if (damageNumber == null)
                damageNumber = numberObj.AddComponent<DamageNumber>();

            damageNumber.ShowDamage(damageResult);
        }

        /// <summary>
        /// Create healing number at position
        /// </summary>
        public void CreateHealingNumber(Vector3 position, TacticalCombatSystem.Core.HealingResult healingResult)
        {
            if (damageNumberPrefab == null) return;

            Vector3 spawnPos = GetRandomizedPosition(position);
            GameObject numberObj = Instantiate(damageNumberPrefab, spawnPos, Quaternion.identity);

            DamageNumber damageNumber = numberObj.GetComponent<DamageNumber>();
            if (damageNumber == null)
                damageNumber = numberObj.AddComponent<DamageNumber>();

            damageNumber.ShowHealing(healingResult);
        }

        /// <summary>
        /// Create miss text at position
        /// </summary>
        public void CreateMissText(Vector3 position)
        {
            if (damageNumberPrefab == null) return;

            Vector3 spawnPos = GetRandomizedPosition(position);
            GameObject numberObj = Instantiate(damageNumberPrefab, spawnPos, Quaternion.identity);

            DamageNumber damageNumber = numberObj.GetComponent<DamageNumber>();
            if (damageNumber == null)
                damageNumber = numberObj.AddComponent<DamageNumber>();

            damageNumber.ShowMiss();
        }

        /// <summary>
        /// Create status effect text at position
        /// </summary>
        public void CreateStatusEffectText(Vector3 position, string effectName, bool isPositive = false)
        {
            if (damageNumberPrefab == null) return;

            Vector3 spawnPos = GetRandomizedPosition(position);
            GameObject numberObj = Instantiate(damageNumberPrefab, spawnPos, Quaternion.identity);

            DamageNumber damageNumber = numberObj.GetComponent<DamageNumber>();
            if (damageNumber == null)
                damageNumber = numberObj.AddComponent<DamageNumber>();

            damageNumber.ShowStatusEffect(effectName, isPositive);
        }

        /// <summary>
        /// Get randomized spawn position
        /// </summary>
        private Vector3 GetRandomizedPosition(Vector3 basePosition)
        {
            Vector3 randomOffset = new Vector3(
                Random.Range(-randomSpread, randomSpread),
                0f,
                Random.Range(-randomSpread, randomSpread)
            );

            return basePosition + spawnOffset + randomOffset;
        }
    }
}
