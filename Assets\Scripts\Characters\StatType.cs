using System;
using System.Collections.Generic;
using UnityEngine;

namespace TacticalCombatSystem.Characters
{
    [Serializable]
    public enum StatType
    {
        // Core Stats
        Level,
        Experience,
        Health,
        MaxHealth,
        Mana,
        MaxMana,

        // Primary Attributes
        Strength,       // Physical damage and carrying capacity
        Intelligence,   // Magic damage and mana pool
        Dexterity,      // Accuracy, critical hit chance, and speed
        Vitality,       // Health and physical defense
        Agility,        // Speed, evasion, and initiative
        Luck,           // Critical chance, status resistance, and random events

        // Secondary Stats (derived from primary attributes)
        PhysicalAttack,
        MagicAttack,
        PhysicalDefense,
        MagicDefense,
        Accuracy,
        Evasion,
        CriticalChance,
        CriticalDamage,
        Speed,
        Initiative,

        // Elemental Affinities
        FireResistance,
        IceResistance,
        LightningResistance,
        EarthResistance,
        WindResistance,
        WaterResistance,
        LightResistance,
        DarkResistance,

        // Status Resistances
        PoisonResistance,
        StunResistance,
        SilenceResistance,
        SleepResistance,
        CharmResistance,
        FearResistance,

        // Special
        ActionPoints,   // For turn order and action economy
        MovementRange,  // For tactical movement
        CarryingCapacity, // For equipment weight limits
        ManaRegeneration, // Mana recovery per turn
        HealthRegeneration // Health recovery per turn
    }

    [Serializable]
    public enum StatModifierType
    {
        Flat,           // +10 Attack
        Percentage,     // +15% Attack
        Multiplicative  // x1.5 Attack
    }

    [Serializable]
    public class StatModifier
    {
        public StatType statType;
        public StatModifierType modifierType;
        public float value;
        public string source; // Equipment, status effect, etc.
        public int priority; // Higher priority modifiers are applied last
        public bool isTemporary; // For status effects
        public float duration; // For temporary modifiers

        public StatModifier(StatType stat, StatModifierType type, float val, string src = "", int prio = 0)
        {
            statType = stat;
            modifierType = type;
            value = val;
            source = src;
            priority = prio;
            isTemporary = false;
            duration = 0f;
        }
    }

    [Serializable]
    public class CharacterStats
    {
        [Header("Base Stats")]
        [SerializeField] private Dictionary<StatType, float> baseStats = new Dictionary<StatType, float>();

        [Header("Current Modifiers")]
        [SerializeField] private List<StatModifier> activeModifiers = new List<StatModifier>();

        // Cached calculated stats for performance
        private Dictionary<StatType, float> cachedStats = new Dictionary<StatType, float>();
        private bool statsDirty = true;

        public CharacterStats()
        {
            InitializeDefaultStats();
        }

        private void InitializeDefaultStats()
        {
            // Initialize all stats to default values
            foreach (StatType stat in System.Enum.GetValues(typeof(StatType)))
            {
                baseStats[stat] = GetDefaultStatValue(stat);
            }
        }

        private float GetDefaultStatValue(StatType stat)
        {
            switch (stat)
            {
                case StatType.Level: return 1f;
                case StatType.MaxHealth: return 100f;
                case StatType.MaxMana: return 50f;
                case StatType.Strength:
                case StatType.Intelligence:
                case StatType.Dexterity:
                case StatType.Vitality:
                case StatType.Agility:
                case StatType.Luck: return 10f;
                case StatType.PhysicalAttack: return 15f;
                case StatType.MagicAttack: return 12f;
                case StatType.PhysicalDefense: return 8f;
                case StatType.MagicDefense: return 6f;
                case StatType.Speed: return 10f;
                case StatType.CriticalChance: return 0.05f; // 5%
                case StatType.CriticalDamage: return 1.5f; // 150%
                case StatType.Accuracy: return 0.9f; // 90%
                case StatType.Evasion: return 0.1f; // 10%
                case StatType.ActionPoints: return 1f;
                case StatType.MovementRange: return 3f;
                default: return 0f;
            }
        }

        public float GetBaseStat(StatType stat)
        {
            return baseStats.TryGetValue(stat, out float value) ? value : 0f;
        }

        public void SetBaseStat(StatType stat, float value)
        {
            baseStats[stat] = value;
            MarkStatsDirty();
        }

        public float GetModifiedStat(StatType stat)
        {
            if (statsDirty)
            {
                RecalculateStats();
            }

            return cachedStats.TryGetValue(stat, out float value) ? value : GetBaseStat(stat);
        }

        public void AddModifier(StatModifier modifier)
        {
            activeModifiers.Add(modifier);
            MarkStatsDirty();
        }

        public void RemoveModifier(StatModifier modifier)
        {
            activeModifiers.Remove(modifier);
            MarkStatsDirty();
        }

        public void RemoveModifiersFromSource(string source)
        {
            activeModifiers.RemoveAll(m => m.source == source);
            MarkStatsDirty();
        }

        private void MarkStatsDirty()
        {
            statsDirty = true;
        }

        private void RecalculateStats()
        {
            cachedStats.Clear();

            foreach (StatType stat in System.Enum.GetValues(typeof(StatType)))
            {
                float finalValue = CalculateStatWithModifiers(stat);
                cachedStats[stat] = finalValue;
            }

            statsDirty = false;
        }

        private float CalculateStatWithModifiers(StatType stat)
        {
            float baseValue = GetBaseStat(stat);

            // Get all modifiers for this stat, sorted by priority
            var modifiers = activeModifiers.FindAll(m => m.statType == stat);
            modifiers.Sort((a, b) => a.priority.CompareTo(b.priority));

            float flatBonus = 0f;
            float percentageMultiplier = 1f;
            float multiplicativeMultiplier = 1f;

            foreach (var modifier in modifiers)
            {
                switch (modifier.modifierType)
                {
                    case StatModifierType.Flat:
                        flatBonus += modifier.value;
                        break;
                    case StatModifierType.Percentage:
                        percentageMultiplier += modifier.value / 100f;
                        break;
                    case StatModifierType.Multiplicative:
                        multiplicativeMultiplier *= modifier.value;
                        break;
                }
            }

            // Apply modifiers in order: base + flat, then percentage, then multiplicative
            float result = (baseValue + flatBonus) * percentageMultiplier * multiplicativeMultiplier;

            // Apply stat-specific constraints
            return ApplyStatConstraints(stat, result);
        }

        private float ApplyStatConstraints(StatType stat, float value)
        {
            switch (stat)
            {
                case StatType.Health:
                    return Mathf.Clamp(value, 0f, GetModifiedStat(StatType.MaxHealth));
                case StatType.Mana:
                    return Mathf.Clamp(value, 0f, GetModifiedStat(StatType.MaxMana));
                case StatType.CriticalChance:
                case StatType.Accuracy:
                case StatType.Evasion:
                    return Mathf.Clamp01(value); // 0-100%
                case StatType.Level:
                    return Mathf.Max(1f, value);
                default:
                    return Mathf.Max(0f, value); // Most stats can't be negative
            }
        }

        public void UpdateTemporaryModifiers(float deltaTime)
        {
            bool removedAny = false;

            for (int i = activeModifiers.Count - 1; i >= 0; i--)
            {
                var modifier = activeModifiers[i];
                if (modifier.isTemporary)
                {
                    modifier.duration -= deltaTime;
                    if (modifier.duration <= 0f)
                    {
                        activeModifiers.RemoveAt(i);
                        removedAny = true;
                    }
                }
            }

            if (removedAny)
            {
                MarkStatsDirty();
            }
        }

        // Helper methods for common stat calculations
        public float GetDerivedStat(StatType derivedStat)
        {
            switch (derivedStat)
            {
                case StatType.PhysicalAttack:
                    return GetBaseStat(StatType.Strength) * 1.5f + GetBaseStat(StatType.Dexterity) * 0.5f;
                case StatType.MagicAttack:
                    return GetBaseStat(StatType.Intelligence) * 1.5f + GetBaseStat(StatType.Luck) * 0.3f;
                case StatType.PhysicalDefense:
                    return GetBaseStat(StatType.Vitality) * 1.2f + GetBaseStat(StatType.Strength) * 0.3f;
                case StatType.MagicDefense:
                    return GetBaseStat(StatType.Intelligence) * 0.8f + GetBaseStat(StatType.Vitality) * 0.7f;
                case StatType.Speed:
                    return GetBaseStat(StatType.Agility) * 1.3f + GetBaseStat(StatType.Dexterity) * 0.7f;
                case StatType.Initiative:
                    return GetBaseStat(StatType.Agility) + GetBaseStat(StatType.Luck) * 0.5f;
                case StatType.CriticalChance:
                    return Mathf.Clamp01((GetBaseStat(StatType.Dexterity) + GetBaseStat(StatType.Luck)) / 200f);
                case StatType.Accuracy:
                    return Mathf.Clamp01(0.8f + GetBaseStat(StatType.Dexterity) / 100f);
                case StatType.Evasion:
                    return Mathf.Clamp01(GetBaseStat(StatType.Agility) / 150f);
                default:
                    return GetModifiedStat(derivedStat);
            }
        }
    }
}
