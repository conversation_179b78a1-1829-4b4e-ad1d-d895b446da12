using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using TacticalCombatSystem.Interfaces;
using TacticalCombatSystem.Interfaces.Core;
using TacticalCombatSystem.Core;
using TacticalCombatSystem.Characters;

namespace TacticalCombatSystem.AI
{
    /// <summary>
    /// AI behavior types for different combat strategies
    /// </summary>
    public enum AIBehaviorType
    {
        Aggressive,     // Focus on dealing damage
        Defensive,      // Focus on survival and protection
        Support,        // Focus on healing and buffing allies
        Tactical,       // Balanced approach with strategic thinking
        Berserker,      // High damage, low defense, reckless
        Guardian,       // Protect allies, tank damage
        Opportunist     // Exploit weaknesses and openings
    }
    
    /// <summary>
    /// AI difficulty levels
    /// </summary>
    public enum AIDifficulty
    {
        Easy,           // Makes suboptimal choices, limited planning
        Normal,         // Decent choices, some planning
        Hard,           // Good choices, strategic planning
        Expert          // Optimal choices, advanced tactics
    }
    
    /// <summary>
    /// Advanced AI system for combat decision-making
    /// </summary>
    public class CombatAI : MonoBehaviour
    {
        [Header("AI Configuration")]
        public AIBehaviorType behaviorType = AIBehaviorType.Tactical;
        public AIDifficulty difficulty = AIDifficulty.Normal;
        
        [Header("Behavior Weights")]
        [Range(0f, 1f)] public float aggressionWeight = 0.5f;
        [Range(0f, 1f)] public float defensiveWeight = 0.3f;
        [Range(0f, 1f)] public float supportWeight = 0.2f;
        [Range(0f, 1f)] public float tacticalWeight = 0.4f;
        
        [Header("Decision Making")]
        public float thinkingTime = 1f; // Time to "think" before acting
        public bool useRandomization = true;
        public float randomizationFactor = 0.1f;
        
        private ICombatParticipant aiCharacter;
        private IBattleManager battleManager;
        private List<ActionEvaluation> lastEvaluations = new List<ActionEvaluation>();
        
        private void Awake()
        {
            aiCharacter = GetComponent<ICombatParticipant>();
            battleManager = BattleManager.Instance;
            
            // Set behavior weights based on type
            SetBehaviorWeights();
        }
        
        /// <summary>
        /// Set behavior weights based on AI type
        /// </summary>
        private void SetBehaviorWeights()
        {
            switch (behaviorType)
            {
                case AIBehaviorType.Aggressive:
                    aggressionWeight = 0.8f;
                    defensiveWeight = 0.1f;
                    supportWeight = 0.1f;
                    tacticalWeight = 0.3f;
                    break;
                    
                case AIBehaviorType.Defensive:
                    aggressionWeight = 0.2f;
                    defensiveWeight = 0.7f;
                    supportWeight = 0.3f;
                    tacticalWeight = 0.5f;
                    break;
                    
                case AIBehaviorType.Support:
                    aggressionWeight = 0.1f;
                    defensiveWeight = 0.3f;
                    supportWeight = 0.8f;
                    tacticalWeight = 0.4f;
                    break;
                    
                case AIBehaviorType.Tactical:
                    aggressionWeight = 0.5f;
                    defensiveWeight = 0.4f;
                    supportWeight = 0.4f;
                    tacticalWeight = 0.8f;
                    break;
                    
                case AIBehaviorType.Berserker:
                    aggressionWeight = 1.0f;
                    defensiveWeight = 0.0f;
                    supportWeight = 0.0f;
                    tacticalWeight = 0.2f;
                    break;
                    
                case AIBehaviorType.Guardian:
                    aggressionWeight = 0.3f;
                    defensiveWeight = 0.9f;
                    supportWeight = 0.6f;
                    tacticalWeight = 0.6f;
                    break;
                    
                case AIBehaviorType.Opportunist:
                    aggressionWeight = 0.6f;
                    defensiveWeight = 0.3f;
                    supportWeight = 0.2f;
                    tacticalWeight = 0.9f;
                    break;
            }
        }
        
        /// <summary>
        /// Make AI decision for current turn
        /// </summary>
        public AIDecision MakeDecision()
        {
            if (aiCharacter == null || battleManager == null)
            {
                return new AIDecision { action = null, target = null, confidence = 0f };
            }
            
            // Get available actions
            var availableActions = aiCharacter.GetAvailableActions();
            if (availableActions == null || availableActions.Count == 0)
            {
                return new AIDecision { action = null, target = null, confidence = 0f };
            }
            
            // Evaluate all possible action-target combinations
            var evaluations = EvaluateAllOptions(availableActions);
            
            // Select best option based on difficulty and behavior
            var bestOption = SelectBestOption(evaluations);
            
            lastEvaluations = evaluations;
            
            return bestOption;
        }
        
        /// <summary>
        /// Evaluate all possible action-target combinations
        /// </summary>
        private List<ActionEvaluation> EvaluateAllOptions(List<ICombatAction> actions)
        {
            var evaluations = new List<ActionEvaluation>();
            
            foreach (var action in actions)
            {
                var potentialTargets = GetPotentialTargets(action);
                
                foreach (var target in potentialTargets)
                {
                    if (action.IsValidTarget(aiCharacter, target))
                    {
                        var evaluation = EvaluateActionTarget(action, target);
                        evaluations.Add(evaluation);
                    }
                }
            }
            
            return evaluations;
        }
        
        /// <summary>
        /// Get potential targets for an action
        /// </summary>
        private List<ICombatParticipant> GetPotentialTargets(ICombatAction action)
        {
            var allParticipants = battleManager.GetAllParticipants();
            var targets = new List<ICombatParticipant>();
            
            foreach (var participant in allParticipants)
            {
                if (participant.IsAlive && action.IsValidTarget(aiCharacter, participant))
                {
                    targets.Add(participant);
                }
            }
            
            return targets;
        }
        
        /// <summary>
        /// Evaluate a specific action-target combination
        /// </summary>
        private ActionEvaluation EvaluateActionTarget(ICombatAction action, ICombatParticipant target)
        {
            var evaluation = new ActionEvaluation
            {
                action = action,
                target = target,
                score = 0f
            };
            
            // Base action value
            evaluation.score += EvaluateActionValue(action);
            
            // Target priority
            evaluation.score += EvaluateTargetPriority(target);
            
            // Situational factors
            evaluation.score += EvaluateSituationalFactors(action, target);
            
            // Behavior-specific modifiers
            evaluation.score += ApplyBehaviorModifiers(action, target, evaluation.score);
            
            // Difficulty-based adjustments
            evaluation.score = ApplyDifficultyModifiers(evaluation.score);
            
            return evaluation;
        }
        
        /// <summary>
        /// Evaluate the inherent value of an action
        /// </summary>
        private float EvaluateActionValue(ICombatAction action)
        {
            float value = 0f;
            
            // Damage potential
            if (action.CanTargetEnemies)
            {
                value += 10f; // Base damage action value
                
                // Higher value for higher damage potential
                // This would need to be implemented based on action properties
                value += action.ManaCost * 0.5f; // Assume higher mana cost = more powerful
            }
            
            // Healing potential
            if (action.CanTargetAllies)
            {
                value += 8f; // Base healing/support value
            }
            
            // Self-targeting (buffs, defend)
            if (action.TargetType == TargetType.Self)
            {
                value += 5f;
            }
            
            // AoE bonus
            if (action.TargetType == TargetType.AllEnemies || action.TargetType == TargetType.AllAllies)
            {
                value += 5f;
            }
            
            return value;
        }
        
        /// <summary>
        /// Evaluate target priority
        /// </summary>
        private float EvaluateTargetPriority(ICombatParticipant target)
        {
            float priority = 0f;
            
            bool isEnemy = target.IsPlayerControlled != aiCharacter.IsPlayerControlled;
            bool isAlly = target.IsPlayerControlled == aiCharacter.IsPlayerControlled;
            
            if (isEnemy)
            {
                // Prioritize low-health enemies
                float healthRatio = (float)target.CurrentHP / target.MaxHP;
                priority += (1f - healthRatio) * 15f;
                
                // Prioritize high-damage enemies
                priority += target.Attack * 0.1f;
                
                // Prioritize support characters
                if (target.MagicAttack > target.Attack)
                {
                    priority += 5f; // Likely a healer/support
                }
            }
            else if (isAlly)
            {
                // Prioritize low-health allies for healing
                float healthRatio = (float)target.CurrentHP / target.MaxHP;
                priority += (1f - healthRatio) * 12f;
                
                // Prioritize important allies
                if (target == aiCharacter)
                {
                    priority += 3f; // Self-preservation
                }
            }
            
            return priority;
        }
        
        /// <summary>
        /// Evaluate situational factors
        /// </summary>
        private float EvaluateSituationalFactors(ICombatAction action, ICombatParticipant target)
        {
            float situational = 0f;
            
            // Health considerations
            float myHealthRatio = (float)aiCharacter.CurrentHP / aiCharacter.MaxHP;
            
            if (myHealthRatio < 0.3f)
            {
                // Low health - prioritize healing and defense
                if (action.TargetType == TargetType.Self || action.CanTargetAllies)
                {
                    situational += 10f;
                }
            }
            
            // Mana considerations
            float myManaRatio = (float)aiCharacter.CurrentMP / aiCharacter.MaxMP;
            
            if (myManaRatio < 0.2f)
            {
                // Low mana - avoid expensive actions
                if (action.ManaCost > aiCharacter.CurrentMP * 0.5f)
                {
                    situational -= 15f;
                }
            }
            
            // Team health considerations
            var allies = battleManager.GetAllParticipants()
                .Where(p => p.IsPlayerControlled == aiCharacter.IsPlayerControlled && p.IsAlive)
                .ToList();
                
            float averageAllyHealth = allies.Average(a => (float)a.CurrentHP / a.MaxHP);
            
            if (averageAllyHealth < 0.4f && action.CanTargetAllies)
            {
                situational += 8f; // Team needs healing
            }
            
            return situational;
        }
        
        /// <summary>
        /// Apply behavior-specific modifiers
        /// </summary>
        private float ApplyBehaviorModifiers(ICombatAction action, ICombatParticipant target, float baseScore)
        {
            float modifier = 0f;
            
            bool isOffensive = action.CanTargetEnemies;
            bool isDefensive = action.TargetType == TargetType.Self || 
                              (action.CanTargetAllies && target.CurrentHP < target.MaxHP);
            bool isSupport = action.CanTargetAllies && target != aiCharacter;
            
            if (isOffensive)
            {
                modifier += baseScore * aggressionWeight;
            }
            
            if (isDefensive)
            {
                modifier += baseScore * defensiveWeight;
            }
            
            if (isSupport)
            {
                modifier += baseScore * supportWeight;
            }
            
            // Tactical considerations
            modifier += baseScore * tacticalWeight * EvaluateTacticalValue(action, target);
            
            return modifier;
        }
        
        /// <summary>
        /// Evaluate tactical value of an action
        /// </summary>
        private float EvaluateTacticalValue(ICombatAction action, ICombatParticipant target)
        {
            // This could include combo potential, positioning, timing, etc.
            // For now, return a simple value
            return 0.1f;
        }
        
        /// <summary>
        /// Apply difficulty-based modifiers
        /// </summary>
        private float ApplyDifficultyModifiers(float score)
        {
            switch (difficulty)
            {
                case AIDifficulty.Easy:
                    // Add some randomness to make suboptimal choices
                    if (useRandomization)
                    {
                        score += UnityEngine.Random.Range(-score * 0.3f, score * 0.1f);
                    }
                    break;
                    
                case AIDifficulty.Normal:
                    if (useRandomization)
                    {
                        score += UnityEngine.Random.Range(-score * 0.15f, score * 0.15f);
                    }
                    break;
                    
                case AIDifficulty.Hard:
                    if (useRandomization)
                    {
                        score += UnityEngine.Random.Range(-score * 0.05f, score * 0.1f);
                    }
                    break;
                    
                case AIDifficulty.Expert:
                    // Minimal randomization for expert AI
                    if (useRandomization)
                    {
                        score += UnityEngine.Random.Range(-score * 0.02f, score * 0.05f);
                    }
                    break;
            }
            
            return score;
        }
        
        /// <summary>
        /// Select the best option from evaluations
        /// </summary>
        private AIDecision SelectBestOption(List<ActionEvaluation> evaluations)
        {
            if (evaluations.Count == 0)
            {
                return new AIDecision { action = null, target = null, confidence = 0f };
            }
            
            // Sort by score
            evaluations.Sort((a, b) => b.score.CompareTo(a.score));
            
            var bestEvaluation = evaluations[0];
            
            // Calculate confidence based on score difference
            float confidence = 0.5f;
            if (evaluations.Count > 1)
            {
                float scoreDifference = bestEvaluation.score - evaluations[1].score;
                confidence = Mathf.Clamp01(0.5f + scoreDifference * 0.1f);
            }
            
            return new AIDecision
            {
                action = bestEvaluation.action,
                target = bestEvaluation.target,
                confidence = confidence,
                reasoning = $"Score: {bestEvaluation.score:F1}"
            };
        }
        
        /// <summary>
        /// Get the last evaluations for debugging
        /// </summary>
        public List<ActionEvaluation> GetLastEvaluations() => lastEvaluations;

        /// <summary>
        /// Force a specific behavior temporarily
        /// </summary>
        public void SetTemporaryBehavior(AIBehaviorType newBehavior, float duration = 0f)
        {
            behaviorType = newBehavior;
            SetBehaviorWeights();

            if (duration > 0f)
            {
                StartCoroutine(ResetBehaviorAfterDelay(duration));
            }
        }

        private System.Collections.IEnumerator ResetBehaviorAfterDelay(float delay)
        {
            var originalBehavior = behaviorType;
            yield return new WaitForSeconds(delay);
            behaviorType = originalBehavior;
            SetBehaviorWeights();
        }
    }
    
    /// <summary>
    /// Represents an AI decision
    /// </summary>
    [Serializable]
    public class AIDecision
    {
        public ICombatAction action;
        public ICombatParticipant target;
        public float confidence; // 0-1, how confident the AI is in this decision
        public string reasoning; // Debug information about why this was chosen
    }
    
    /// <summary>
    /// Evaluation of an action-target combination
    /// </summary>
    [Serializable]
    public class ActionEvaluation
    {
        public ICombatAction action;
        public ICombatParticipant target;
        public float score;
        public Dictionary<string, float> scoreBreakdown = new Dictionary<string, float>();
    }
}
